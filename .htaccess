<IfModule mod_rewrite.c>
<IfModule mod_env.c>
SetEnv HTTP_MOD_REWRITE On
</IfModule>

RewriteEngine on

RewriteRule . - [E=REWRITEBASE:/]
RewriteRule ^api(?:/(.*))?$ %{ENV:REWRITEBASE}webservice/dispatcher.php?url=$1 [QSA,L]
RewriteRule ^upload/.+$ %{ENV:REWRITEBASE}index.php [QSA,L]

# AlphaImageLoader for IE and fancybox
RewriteRule ^media/catalog/product/(.*)$ %{ENV:REWRITEBASE}readimg.php?p=$1 [QSA,L]
RewriteRule ^images_ie/?([^/]+)\.(jpe?g|png|gif)$ %{ENV:REWRITEBASE}js/jquery/plugins/fancybox/images/$1.$2 [L]

# Dispatcher
RewriteCond %{REQUEST_FILENAME} -s [OR]
RewriteCond %{REQUEST_FILENAME} -l [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^.*$ - [NC,L]
RewriteRule ^.*$ %{ENV:REWRITEBASE}index.php [NC,L]

RewriteRule . - [E=REWRITEBASE:/]
RewriteRule ^api(?:/(.*))?$ %{ENV:REWRITEBASE}webservice/dispatcher.php?url=$1 [QSA,L]
RewriteRule ^upload/.+$ %{ENV:REWRITEBASE}index.php [QSA,L]

# AlphaImageLoader for IE and fancybox
RewriteRule ^images_ie/?([^/]+)\.(jpe?g|png|gif)$ %{ENV:REWRITEBASE}js/jquery/plugins/fancybox/images/$1.$2 [L]

# Dispatcher
RewriteCond %{REQUEST_FILENAME} -s [OR]
RewriteCond %{REQUEST_FILENAME} -l [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^.*$ - [NC,L]
RewriteRule ^.*$ %{ENV:REWRITEBASE}index.php [NC,L]
</IfModule>

AddType application/vnd.ms-fontobject .eot
AddType font/ttf .ttf
AddType font/otf .otf
AddType application/font-woff .woff
AddType font/woff2 .woff2
<IfModule mod_headers.c>
	<FilesMatch "\.(ttf|ttc|otf|eot|woff|woff2|svg)$">
		Header set Access-Control-Allow-Origin "*"
	</FilesMatch>
</IfModule>

#If rewrite mod isn't enabled
ErrorDocument 404 /index.php?controller=404
