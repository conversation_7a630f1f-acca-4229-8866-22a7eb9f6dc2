<?php
use PrestaShop\PrestaShop\Adapter\Image\ImageRetriever;
use PrestaShop\PrestaShop\Adapter\Presenter\AbstractLazyArray;
use PrestaShop\PrestaShop\Adapter\Presenter\Product\ProductLazyArray;
use PrestaShop\PrestaShop\Core\Domain\Product\ValueObject\RedirectType;
use PrestaShop\PrestaShop\Core\Product\ProductExtraContentFinder;

class ProductControllerCore extends ProductPresentingFrontControllerCore
{
    /** @var string */
    public $php_self = 'product';

    /** @var int */
    protected $id_product;

    /** @var int|null */
    protected $id_product_attribute;

    /** @var Product */
    protected $product;

    /** @var Category|null */
    public $category;

    /**
     * @var array 筛选条件
     */
    protected $query_arr = [];

    /*
     * @var array 所有筛选分类ID
     */
    protected $query_category_ids = [];

    /**
     * @var string url rewrite
     */
    protected $urlRewrite;

    /**
     * @var int 分页页面产品数量
     */
    protected $nb_products_per_page;

    protected $redirectionExtraExcludedKeys = ['id_product_attribute', 'rewrite'];

    /**
     * @var array
     */
    protected $combinations;

    protected $quantity_discounts;

    /**
     * @var array
     */
    protected $adminNotifications = [];

    /**
     * @var bool
     */
    protected $isQuickView = false;

    /**
     * @var bool
     */
    protected $isPreview = false;

    /**
     * 选中图片的id
     * @var string
     */
    protected $check_image_id = '';

    protected $notFound = false;

    /**
     * 验证链接是否正确
     * @param mixed $canonical_url
     * @return void
     */
    public function canonicalRedirection($canonical_url = '')
    {
        // This is there to prevent error, because this function is also called
        // in parent front controller before we have even loaded our data.
        if (!Validate::isLoadedObject($this->product)) {
            return;
        }

        // We check if the combination is valid, if not, we reset it redirect to pure product URL without combination.
        if (!$this->product->hasCombinations() || !$this->isValidCombination($this->id_product_attribute, $this->product->id)) {
            unset($_GET['id_product_attribute']);
            $this->id_product_attribute = null;
        }

        // If the attribute id is present in the url we use it to perform the redirection, this will fix any domain
        // or rewriting error and redirect to the appropriate url.
        parent::canonicalRedirection($this->context->link->getProductLink(
            $this->product,
            null,
            null,
            null,
            null,
            null,
            $this->id_product_attribute
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getCanonicalURL(): string
    {
        $product = $this->context->smarty->getTemplateVars('product');
        if (!($product instanceof ProductLazyArray)) {
            return '';
        }
        return $product->getCanonicalUrl();
    }

    /**
     * 初始化
     *
     * @see FrontController::init()
     */
    public function init()
    {
        parent::init();

        if (
            Tools::isSubmit('pid')
            && Tools::isSubmit('cid')
            && (Tools::isSubmit('fbclid')
                || Tools::isSubmit('gclid')
                || Tools::isSubmit('dclid')
                || Tools::isSubmit('ttclid')
                || Tools::isSubmit('msclkid')
                || Tools::isSubmit('epik'))
        ) {
            //如果通过广告链接进入产品页，直接展示分类页信息，并将当前产品放在第一个
            $this->displayCategory();
        } else {
            $this->id_product = (int) Tools::getValue('id_product');
            $this->id_product_attribute = (int) Tools::getValue('id_product_attribute', null);
            // 根据链接上的SKU获取产品ID
            $this->supplier_reference = (string) Tools::getValue('supplier_reference');
            if (!$this->id_product) {
                $this->id_product = Db::getInstance()->getValue(
                    'SELECT `id_product`
                    FROM `' . _DB_PREFIX_ . 'product`
                    WHERE `supplier_reference` = \'' . pSQL($this->supplier_reference) . '\''
                );
                $_GET['id_product'] = $this->id_product;
            }
            // Load viewing modes
            if ('1' === Tools::getValue('quickview')) {
                $this->setQuickViewMode();
            }

            // We are in a preview mode only if proper admin token was also provided in the URL
            if (
                '1' === Tools::getValue('preview') && Tools::getValue('adtoken') == Tools::getAdminToken(
                    'AdminProducts'
                    . (int) Tab::getIdFromClassName('AdminProducts')
                    . (int) Tools::getValue('id_employee')
                )
            ) {
                $this->setPreviewMode();
            }

            // 初始化产品对象，失败则404
            if ($this->id_product) {
                $this->product = new Product($this->id_product, true, $this->context->language->id, $this->context->shop->id);
            }
            if (!Validate::isLoadedObject($this->product) || !$this->product->active) {
                // todo open
                header('HTTP/1.1 404 Not Found');
                header('Status: 404 Not Found');
                $pageSize = (int) Tools::getValue('pageSize', 8);
                $notFundProducts = (new ProductCore())->getNotfundProducts(1, $pageSize);
                $this->context->smarty->assign([
                    'noFoundImg' => 'https://' . _PS_IMAGE_DOMAIN_ . '/media/catalog/resources/static/not-found-pc.jpg',
                    'noFoundImgMobile' => 'https://' . _PS_IMAGE_DOMAIN_ . '/media/catalog/resources/static/not-found-mobile.jpg',
                    'notFundProducts' => $notFundProducts,
                ]);
                $this->errors[] = $this->trans('This product is no longer available.', [], 'Shop.Notifications.Error');
                $this->setTemplate('errors/404');
                return;
            }

            // 验证链接是否正确
            $this->canonicalRedirection();

            // 设置模板文件
            $this->setTemplate('catalog/product', [
                'entity' => 'product',
                'id' => $this->id_product,
            ]);

            // 验证查看权限
            $this->checkPermissionsToViewProduct();

            // 初始化读取的产品分类 -- 作用未知
            $this->initializeCategory();

        }
    }

    /**
     * 验证查看权限
     */
    public function checkPermissionsToViewProduct()
    {
        // If the person accessing the product page is admin with proper token, we only do limited checks
        if ($this->isPreview()) {
            if (!$this->product->isAssociatedToShop() || !$this->product->active) {
                $this->adminNotifications['inactive_product'] = [
                    'type' => 'warning',
                    'message' => $this->trans('This product is not visible to your customers.', [], 'Shop.Notifications.Warning'),
                ];
            }

            return;
        }

        // Now the checks for public
        // If product is disabled or doesn't belong to this shop, we respect the redirection settings
        if (!$this->product->isAssociatedToShop() || !$this->product->active) {
            if (!$this->product->id_type_redirected) {
                if (in_array($this->product->redirect_type, [RedirectType::TYPE_CATEGORY_PERMANENT, RedirectType::TYPE_CATEGORY_TEMPORARY])) {
                    $this->product->id_type_redirected = $this->product->id_category_default;
                }
            } elseif (in_array($this->product->redirect_type, [RedirectType::TYPE_PRODUCT_PERMANENT, RedirectType::TYPE_PRODUCT_TEMPORARY]) && $this->product->id_type_redirected == $this->product->id) {
                $this->product->redirect_type = RedirectType::TYPE_NOT_FOUND;
            }

            $redirect_type = $this->product->redirect_type;
            // If product has no specified redirect settings, we get default from configuration
            if (empty($redirect_type) || $redirect_type == RedirectType::TYPE_DEFAULT) {
                $redirect_type = Configuration::get('PS_PRODUCT_REDIRECTION_DEFAULT');
            }

            switch ($redirect_type) {
                case RedirectType::TYPE_PRODUCT_PERMANENT:
                    header('HTTP/1.1 301 Moved Permanently');
                    header('Location: ' . $this->context->link->getProductLink($this->product->id_type_redirected));
                    exit;
                case RedirectType::TYPE_PRODUCT_TEMPORARY:
                    header('HTTP/1.1 302 Moved Temporarily');
                    header('Cache-Control: no-cache');
                    header('Location: ' . $this->context->link->getProductLink($this->product->id_type_redirected));
                    exit;
                case RedirectType::TYPE_CATEGORY_PERMANENT:
                    header('HTTP/1.1 301 Moved Permanently');
                    header('Location: ' . $this->context->link->getCategoryLink($this->product->id_type_redirected));
                    exit;
                case RedirectType::TYPE_CATEGORY_TEMPORARY:
                    header('HTTP/1.1 302 Moved Temporarily');
                    header('Cache-Control: no-cache');
                    header('Location: ' . $this->context->link->getCategoryLink($this->product->id_type_redirected));
                    exit;
                case RedirectType::TYPE_SUCCESS_DISPLAYED:
                    break;
                case RedirectType::TYPE_NOT_FOUND_DISPLAYED:
                    // We want to send this response only on initial page load
                    // Sending it for ajax requests can cause problems in scripts relying on 200 response
                    if (!$this->ajax) {
                        header('HTTP/1.1 404 Not Found');
                        header('Status: 404 Not Found');
                    }

                    break;
                case RedirectType::TYPE_GONE_DISPLAYED:
                    // We want to send this response only on initial page load
                    // Sending it for ajax requests can cause problems in scripts relying on 200 response
                    if (!$this->ajax) {
                        header('HTTP/1.1 410 Gone');
                        header('Status: 410 Gone');
                    }

                    break;
                case RedirectType::TYPE_GONE:
                    header('HTTP/1.1 410 Gone');
                    header('Status: 410 Gone');
                    $this->errors[] = $this->trans('This product is no longer available.', [], 'Shop.Notifications.Error');
                    $this->setTemplate('errors/410');

                    break;
                case RedirectType::TYPE_NOT_FOUND:
                default:
                    header('HTTP/1.1 404 Not Found');
                    header('Status: 404 Not Found');
                    $this->errors[] = $this->trans('This product is no longer available.', [], 'Shop.Notifications.Error');
                    $this->setTemplate('errors/404');

                    break;
            }
        }

        // Check if customer is allowed to access this product
        if (!$this->product->checkAccess(isset($this->context->customer->id) && $this->context->customer->id ? (int) $this->context->customer->id : 0)) {
            header('HTTP/1.1 403 Forbidden');
            header('Status: 403 Forbidden');
            $this->errors[] = $this->trans('You do not have access to this product.', [], 'Shop.Notifications.Error');
            $this->setTemplate('errors/forbidden');
        }
    }

    /**
     * 初始化产品分类 -- 作用未知(获取当前用户点击产品的所属分类(直接的分类或者分类进的))
     */
    public function initializeCategory()
    {
        $id_category = false;
        if (
            isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] == Tools::secureReferrer($_SERVER['HTTP_REFERER']) // Assure us the previous page was one of the shop
            && preg_match('~^.*(?<!\/content)\/([0-9]+)\-(.*[^\.])|(.*)id_(category|product)=([0-9]+)(.*)$~', $_SERVER['HTTP_REFERER'], $regs)
        ) {
            // If the previous page was a category and is a parent category of the product use this category as parent category
            $id_object = false;
            if (isset($regs[1]) && is_numeric($regs[1])) {
                $id_object = (int) $regs[1];
            } elseif (isset($regs[5]) && is_numeric($regs[5])) {
                $id_object = (int) $regs[5];
            }
            if ($id_object) {
                $referers = [$_SERVER['HTTP_REFERER'], urldecode($_SERVER['HTTP_REFERER'])];
                if (in_array($this->context->link->getCategoryLink($id_object), $referers)) {
                    $id_category = (int) $id_object;
                } elseif (isset($this->context->cookie->last_visited_category) && (int) $this->context->cookie->last_visited_category && in_array($this->context->link->getProductLink($id_object), $referers)) {
                    $id_category = (int) $this->context->cookie->last_visited_category;
                }
            }
        }
        if (!$id_category || !Category::inShopStatic($id_category, $this->context->shop) || !Product::idIsOnCategoryId((int) $this->product->id, ['0' => ['id_category' => $id_category]])) {
            $id_category = (int) $this->product->id_category_default;
        }

        // Load category and store it in cookie
        $this->category = new Category((int) $id_category, (int) $this->context->cookie->id_lang);
        $this->context->cookie->last_visited_category = (int) $this->category->id_category;
    }

    /**
     * 产品页内容主体
     *
     * @see FrontController::initContent()
     */
    public function initContent()
    {
        if (
            !$this->errors
            && !(Tools::isSubmit('pid')
                && Tools::isSubmit('cid')
                && (Tools::isSubmit('fbclid')
                    || Tools::isSubmit('gclid')
                    || Tools::isSubmit('dclid')
                    || Tools::isSubmit('ttclid')
                    || Tools::isSubmit('msclkid')
                    || Tools::isSubmit('epik')))
        ) {
            $redis = Tools::getRedis();
            // 清空分类id 最热产品默认id
            $this->context->cookie->id_categories = json_encode([]);
            // 处理产品描述中的图片
            $this->product->description = $this->transformDescriptionWithImg($this->product->description);
            // 产品原价
            $productPriceWithoutReduction = $this->product->getPriceWithoutReduct(false, null);
            // 传递产品所属的分类到前端
            $this->assignCategory();
            // 获取礼服尺寸表，并传到前台
            $this->assignAllSizeChart();
            // 所有尺寸对应显示的三条数据
            $this->context->smarty->assign($this->getSizeChart());
            // 获取产品需要传给前台的变量
            if ($redis->get(_PS_REDIS_PRODUCT_DETAIL_ . $this->product->id)) { //产品信息存redis 统一使用分类页产品缓存
                $product_for_template = json_decode($redis->get(_PS_REDIS_PRODUCT_DETAIL_ . $this->product->id), true);
                $product_for_template['base_price_amount'] = Tools::convertPrice(floatval($product_for_template['current_price']));
                $product_for_template['base_price'] = Tools::displayPrice(Tools::convertPrice(floatval($product_for_template['current_price'])));
                $product_for_template['regular_base_price'] = Tools::displayPrice(Tools::convertPrice(floatval($product_for_template['initial_original_price'])));
                //获取当前选中变体
                $id_product_attribute = $this->getIdProductAttributeByGroupOrRequestOrDefault();
                if (isset($id_product_attribute) && $id_product_attribute != 0) {
                    $attributes = Product::getAttributesParams($this->product->id, $id_product_attribute);
                    $product_for_template['attributes'] = [];
                    foreach ($attributes as $attribute) {
                        $product_for_template['attributes'][$attribute['id_attribute_group']] = $attribute;
                    }
                    //查询选中变体的价格
                    $price = Product::getPriceStatic($this->product->id, false, $id_product_attribute);
                    $product_for_template['price'] = Tools::displayPrice(floatval($price));
                }
                $cart_id = (int) $this->context->cart->id;
                //根据变体跟据购物车id获取定制信息
                $query = new DbQuery();
                $query->select('id_cart_product, id_customization')
                    ->from('cart_product')
                    ->where('id_cart = ' . (int) $cart_id)
                    ->where('id_product = ' . (int) $this->product->id)
                    ->where('id_product_attribute = ' . (int) $id_product_attribute);

                $id_cart_product = Db::getInstance()->getRow($query);
                if ($id_cart_product) {
                    $product_for_template['id_cart_product'] = $id_cart_product['id_cart_product'];
                    $product_for_template['id_customization'] = $id_cart_product['id_customization'];
                }
                $product_for_template['id_product_attribute'] = $id_product_attribute;
            } else {
                $product_data = $this->getTemplateVarProduct();
                $product_for_template = $this->formatProduct($product_data);
                $redis->set(_PS_REDIS_PRODUCT_DETAIL_ . $this->product->id, json_encode($product_for_template));
            }
            //单独的产品图片，用于实现手指放大功能
            $product_data = Product::getProductData($this->product->id);
            $product_images = $product_data['images']['product_images'];
            $video_url = $product_data['video_url'] ?? '';
            if ($product_images && $video_url) {
                array_unshift($product_images, $product_images[0]);
            }
            //找到所有可用的优惠券规则
            $cateRule = new CartRule();
            $discounts = $cateRule->getAllProductRulesByProductId($this->product->id);
            //布料颜色分类链接，在后台module设置选用的分类
            $swatch_cate = Configuration::get('SWATCHES_CATEGORY') ?: 0;
            $colorSwatchUrl = null;
            if ($swatch_cate) {
                $colorSwatchUrl = $this->context->link->getCategoryLink($swatch_cate);
            }
            // 此处可看到assign的内容
            // 评论星级
            $averageTotal = $this->product->getAverageGrade();
            //关联产品的变体信息 如果有链接有颜色筛选把默认颜色的图片返回回来
            $getAttributesGroups = $this->assignAttributesGroups($product_for_template);
            $shown_color_image = $getAttributesGroups['shown_color_image'];
            if ($shown_color_image && !empty($product_for_template['default_image'])) {
                if (is_array($product_for_template['default_image'])) {
                    $default_image = $product_for_template['default_image'];
                } elseif ($product_for_template['default_image'] instanceof Traversable) {
                    $default_image = iterator_to_array($product_for_template['default_image']);
                } else {
                    $default_image = [];
                }

                $default_image['shown_color_image'] = $shown_color_image;
                $product_for_template['default_image'] = $default_image;
            } else {
                if (is_array($product_for_template['default_image'])) {
                    $default_image = $product_for_template['default_image'];
                } elseif ($product_for_template['default_image'] instanceof Traversable) {
                    $default_image = iterator_to_array($product_for_template['default_image']);
                } else {
                    $default_image = [];
                }

                if ((bool) Configuration::get('PS_PRODUCT_IMAGES_WATERMARK') == true) {
                    $default_image['shown_color_image'] = $default_image['bySize']['watermark']['url'] ?? '';
                } else {
                    $default_image['shown_color_image'] = $default_image['bySize']['home_default']['url'] ?? '';
                }

                $product_for_template['default_image'] = $default_image;
            }


            //手机端传颜色后图片位置重组
            if ($this->check_image_id && $this->context->isMobile()) {
                if (isset($product_for_template['images'])) {
                    //根据传来的id进行重组排序，传来的放第一位
                    $images = $product_for_template['images'];
                    $new_images = [];
                    for ($i = 0; $i < count($images); $i++) {
                        if ($images[$i]['id_image'] == $this->check_image_id) {
                            $new_images[] = $images[$i];
                            unset($images[$i]);
                        }
                    }
                    $new_images = array_merge($new_images, $images);
                    $product_for_template['images'] = $new_images;
                }
            }
            //将选中的颜色对应的图片默认为true
            if (!empty($product_for_template['images'])) {
                $images = is_array($product_for_template['images']) ? $product_for_template['images'] : iterator_to_array($product_for_template['images']);
                foreach ($images as $key => $value) {
                    $images[$key]['is_default'] = ($value['id_image'] == $this->check_image_id);
                }
                $product_for_template['images'] = $images;

            }
            $check_image_type = false;
            if ($this->check_image_id) {
                $check_image_type = true;
            }
            $this->context->smarty->assign([
                'product_images' => $product_images,
                'productPriceWithoutReduction' => $productPriceWithoutReduction,
                'accessories' => $this->getAccessories(),
                'product' => $product_for_template,
                'watermark' => (bool) Configuration::get('PS_PRODUCT_IMAGES_WATERMARK'),
                'check_image_type' => $check_image_type,
                'product_discounts' => $discounts,
                'colorSwatchUrl' => $colorSwatchUrl,
                'paypal_client_id' => Configuration::get('PAYPAL_PO_CLIENT_ID') ?: '',
                'productAverageTotal' => $averageTotal['grade'] ? round($averageTotal['grade'], 2) : 0,
                'currency_sign' => $this->context->currency->sign,
                'cutting_time_min' => (int) Configuration::get('PS_MIN_CUTTING_TIME'),
                'cutting_time_max' => (int) Configuration::get('PS_MAX_CUTTING_TIME'),
                'carrier_time_min' => (int) Configuration::get('PS_MIN_CARRIER_TIME'),
                'carrier_time_max' => (int) Configuration::get('PS_MAX_CARRIER_TIME'),
            ]);

            // 48小事发货的变体信息
            $this->assignAttributesGroupsOnlyShipIn48hrs($product_for_template);
        }

        parent::initContent();
    }

    /**
     * Processes submitted customizations
     *
     * @see FrontController::postProcess()
     */
    public function postProcess()
    {
        //产品原生自定义属性提交
        if (Tools::isSubmit('submitCustomizedData')) {
            // If cart has not been saved, we need to do it so that customization fields can have an id_cart
            // We check that the cookie exists first to avoid ghost carts
            if (!$this->context->cart->id && isset($_COOKIE[$this->context->cookie->getName()])) {
                $this->context->cart->add();
                $this->context->cookie->id_cart = (int) $this->context->cart->id;
            }
            $this->pictureUpload();
            $this->textRecord();
        } elseif (Tools::getIsset('deletePicture') && !$this->context->cart->deleteCustomizationToProduct($this->product->id, Tools::getValue('deletePicture'))) {
            $this->errors[] = $this->trans('An error occurred while deleting the selected picture.', [], 'Shop.Notifications.Error');
        }
    }

    // 弹框数据
    public function displayAjaxProductInfo()
    {
        // 产品数据
        $productForTemplate = $this->getTemplateVarProduct();
        // 产品所有的属性
        $attributes_groups = $this->product->getAttributesGroups($this->context->language->id);
        $result = $this->getAttributesGroups($attributes_groups, $productForTemplate);
        // 产品48小时发货属性
        $ship_in_48hrs_attributes_groups = $this->product->getAttributesGroups($this->context->language->id, null, true);
        $ship_in_48hrs_result = $this->getAttributesGroups($ship_in_48hrs_attributes_groups, $productForTemplate);

        // 尺寸表三条数据的所有信息和定制尺寸图片
        $size_charts_result = $this->getSizeChart();

        // 重置attributes的key
        foreach ($result['groups'] as $key => $value) {
            $result['groups'][$key]['attributes'] = array_values($result['groups'][$key]['attributes']);
        }

        // 传递数据
        $this->ajaxRender(json_encode([
            'product' => $productForTemplate,
            'groups' => $result['groups'],
            'ship_in_48hrs_groups' => $ship_in_48hrs_result['groups'],
            'size_charts' => $size_charts_result['size_charts'],
            'custom_size_image_url' => $size_charts_result['custom_size_image_url'],
            'custom_size_image_large_url' => $size_charts_result['custom_size_image_large_url'],
        ]));
    }


    // 自带的快速浏览
    public function displayAjaxQuickview()
    {
        $productForTemplate = $this->getTemplateVarProduct();
        ob_end_clean();
        header('Content-Type: application/json');

        $this->setQuickViewMode();
        $id_cart_product = (int) Tools::getValue('id_cart_product');
        $custom_size = Db::getInstance()->getRow('SELECT `unit`, `bust`, `waist`, `hips`, `hollow_to_floor`,`height`, `extra_length`, `arm` FROM `' . _DB_PREFIX_ . 'cart_product_custom_size` WHERE `id_cart_product` = ' . (int) $id_cart_product);

        $this->ajaxRender(json_encode([
            'quickview_html' => $this->render(
                'catalog/_partials/quickview',
                $productForTemplate instanceof AbstractLazyArray ?
                $productForTemplate->jsonSerialize() :
                $productForTemplate
            ),
            'product' => $productForTemplate,
            'id_cart_product' => (int) Tools::getValue('id_cart_product'),
            'custom_size' => $custom_size ?: []
        ]));
    }

    public function displayAjaxRefresh()
    {
        $product = $this->getTemplateVarProduct();
        $minimalProductQuantity = $this->getProductMinimalQuantity($product);

        ob_end_clean();
        header('Content-Type: application/json');
        $this->ajaxRender(json_encode([
            'product_prices' => $this->render('catalog/_partials/product-prices'),
            'product_cover_thumbnails' => $this->render('catalog/_partials/product-cover-thumbnails'),
            'product_customization' => $this->render(
                'catalog/_partials/product-customization',
                [
                    'customizations' => $product['customizations'],
                ]
            ),
            'product_details' => $this->render('catalog/_partials/product-details'),
            'product_variants' => $this->render('catalog/_partials/product-variants'),
            'product_discounts' => $this->render('catalog/_partials/product-discounts'),
            'product_add_to_cart' => $this->render('catalog/_partials/product-add-to-cart'),
            'product_additional_info' => $this->render('catalog/_partials/product-additional-info'),
            'product_images_modal' => $this->render('catalog/_partials/product-images-modal'),
            'product_flags' => $this->render('catalog/_partials/product-flags'),
            'product_url' => $this->context->link->getProductLink(
                $product['id_product'],
                null,
                null,
                null,
                $this->context->language->id,
                null,
                $product['id_product_attribute'],
                false,
                false,
                true,
                $this->isPreview() ? ['preview' => '1'] : []
            ),
            'product_minimal_quantity' => $minimalProductQuantity,
            'product_has_combinations' => !empty($this->combinations),
            'id_product_attribute' => $product['id_product_attribute'],
            'id_customization' => $product['id_customization'],
            'product_title' => $this->getTemplateVarPage()['meta']['title'],
            'is_quick_view' => $this->isQuickView(),
        ]));
    }

    /**
     * 关联产品的变体信息
     */
    protected function assignAttributesGroups($product_for_template = null)
    {
        // 定制属性是否被选中
        $is_custom_size_selected = false;

        // 产品所有的属性
        $attributes_groups = $this->product->getAttributesGroups($this->context->language->id);

        $result = $this->getAttributesGroups($attributes_groups, $product_for_template);
        $this->combinations = $result['combinations'];
        $this->context->smarty->assign([
            'groups' => $result['groups'],
            'colors' => $result['colors'],
            'combinations' => $result['combinations'],
            'combinationImages' => $result['combinationImages'],
            'attribute_count' => $result['attribute_count'],
            'is_custom_size_selected' => $is_custom_size_selected,
        ]);
        return $result;
    }

    /**
     * 48小事发货的变体信息
     */
    protected function assignAttributesGroupsOnlyShipIn48hrs($product_for_template = null)
    {
        // 定制属性是否被选中
        $is_custom_size_selected = false;
        // 产品48小时的属性
        $attributes_groups = $this->product->getAttributesGroups($this->context->language->id, null, true);
        $result = $this->getAttributesGroups($attributes_groups, $product_for_template);

        // 整理属性按变体
        $groups_by_product_attribute = [];
        foreach ($attributes_groups as $group) {
            $groups_by_product_attribute[$group['id_product_attribute']][] = $group;
        }

        // 颜色对应尺寸
        $ship_in_48hrs_color_size = [];
        $ship_in_48hrs_attr = [];
        // 整理数量
        $ship_in_48hrs_quantitys = [];
        // 整理数据
        $id_color_attribute = 0;
        foreach ($groups_by_product_attribute as $id_product_attribute => $groups) {
            foreach ($groups as $group) {
                if ($group['group_type'] == 'color' && !isset($ship_in_48hrs_color_size[$group['id_attribute']])) {
                    $id_color_attribute = $group['id_attribute'];
                    $ship_in_48hrs_color_size[$id_color_attribute] = [];
                } elseif ($group['group_type'] == 'size' && isset($ship_in_48hrs_color_size[$id_color_attribute])) {
                    if (!in_array($group['id_attribute'], $ship_in_48hrs_color_size[$id_color_attribute])) {
                        $ship_in_48hrs_color_size[$id_color_attribute][] = (int) $group['id_attribute'];
                    }
                }
                // 整理判断48小时的属性，按group的ID来排
                $ship_in_48hrs_attr[$id_product_attribute][$group['id_attribute_group']] = $group['id_attribute'];
                if (!isset($ship_in_48hrs_quantitys[$id_product_attribute])) {
                    $ship_in_48hrs_quantitys[$id_product_attribute] = (int) $group['quantity_48hrs'];
                }
            }
        }

        // 判断是否是48小时的数据
        $ship_in_48hrs_implode = [];
        foreach ($ship_in_48hrs_attr as $id_product_attribute => $attr) {
            ksort($attr);
            $ship_in_48hrs_implode[] = implode('-', $attr);
        }

        $this->context->smarty->assign([
            'ship_in_48hrs_groups' => $result['groups'],
            'ship_in_48hrs_colors' => $result['colors'],
            'ship_in_48hrs_combinations' => $result['combinations'],
            'ship_in_48hrs_combinationImages' => $result['combinationImages'],
            'ship_in_48hrs_attribute_count' => $result['attribute_count'],
            'ship_in_48hrs_is_custom_size_selected' => $is_custom_size_selected,
            'ship_in_48hrs_color_size' => $ship_in_48hrs_color_size ?? [],
            'ship_in_48hrs_implode' => $ship_in_48hrs_implode,
            'ship_in_48hrs_quantity' => array_sum($ship_in_48hrs_quantitys),
        ]);
    }

    /**
     * 获取产品属性
     * @param mixed $attributes_groups
     * @param mixed $product_for_template
     * @return array
     */
    private function getAttributesGroups($attributes_groups, $product_for_template = null)
    {
        // 属于颜色的产品属性['id_attribute'][]
        $colors = [];
        // 属性分组
        $groups = [];
        //所有变体信息
        //url中shown_color对应的产品主图
        $shown_color_image = '';
        $combinations = [];
        if (is_array($attributes_groups) && $attributes_groups) {
            // 所有变体的图片
            $combination_images = $this->product->getCombinationImages($this->context->language->id);
            $variant_images = [];
            $variant_image_homes = [];
            // 变体的价格
            $combination_prices_set = [];
            //获取url中的shown_color值
            $shown_color = Tools::getValue('shown_color');
            if ($shown_color) {
                $shown_color = explode(',', $shown_color);
                //默认第一个并且转小写
                $shown_color_0 = strtolower($shown_color[0]);
                $shown_color_0 = trim(preg_replace('/\s+/', ' ', preg_replace('/[\_?]+/', ' ', $shown_color_0)));
            }
            foreach ($attributes_groups as $k => $row) {
                //变体图片
                $variant_image = '';
                //变体对应的大图
                $variant_image_home = '';
                //变体图片ID
                $variant_id_image = $row['id_product_attribute_image'];
                if ($variant_id_image) {
                    if (!isset($variant_images[$variant_id_image])) {
                        $variant_images[$variant_id_image] = $this->context->link->getImageLink($this->product->link_rewrite, $variant_id_image, ImageType::getFormattedName('small'));
                        //判断是否启用水印图
                        if ((bool) Configuration::get('PS_PRODUCT_IMAGES_WATERMARK') == true) {
                            $variant_image_homes[$variant_id_image] = $this->context->link->getImageLink($this->product->link_rewrite, $variant_id_image, 'watermark');
                        } else {
                            $variant_image_homes[$variant_id_image] = $this->context->link->getImageLink($this->product->link_rewrite, $variant_id_image, ImageType::getFormattedName('home'));
                        }
                    }
                    $variant_image = $variant_images[$variant_id_image];
                    $variant_image_home = $variant_image_homes[$variant_id_image];
                }
                // 属于颜色类型的属性，并且有填写颜色代码或者存在属性图片
                if (isset($row['is_color_group']) && $row['is_color_group']) {
                    //产品属性
                    if (!isset($colors[$row['id_attribute']])) {
                        $colors[$row['id_attribute']]['id'] = $row['id_attribute'];
                        $colors[$row['id_attribute']]['value'] = $row['attribute_color'];
                        $colors[$row['id_attribute']]['name'] = Tools::translate($row['attribute_name']);
                        $colors[$row['id_attribute']]['attributes_quantity'] = 0;
                        $colors[$row['id_attribute']]['texture'] = @filemtime(_PS_COL_IMG_DIR_ . $row['id_attribute'] . '.jpg') ? _THEME_COL_DIR_ . $row['id_attribute'] . '.jpg' : '';
                        $colors[$row['id_attribute']]['attribute_color_code'] = strtolower($row['code']);
                    }
                    $colors[$row['id_attribute']]['attributes_quantity'] += (int) $row['quantity'];
                    // 属性对应变体图片
                    $colors[$row['id_attribute']]['product_attribute_image_id'] = $variant_id_image;
                    if (!isset($colors[$row['id_attribute']]['product_attribute_image']) && $variant_image) {
                        $colors[$row['id_attribute']]['product_attribute_image'] = $variant_image;
                    }
                    // 色卡图片
                    if (!isset($colors[$row['id_attribute']]['material_color_image']) && $row['material_color_image']) {
                        $colors[$row['id_attribute']]['material_color_image'] = 'https://' . _PS_IMAGE_DOMAIN_ . '/media/catalog/' . $row['material_color_image'];
                    }
                }
                // 属性分组
                if (!isset($groups[$row['id_attribute_group']])) {
                    $groups[$row['id_attribute_group']] = [
                        'group_name' => $row['group_name'],
                        'name' => Tools::translate($row['public_group_name']),
                        'group_type' => $row['group_type'],
                        'default' => -1,
                    ];
                }
                // 判断属性是否被选中
                $selected = (isset($product_for_template['attributes'][$row['id_attribute_group']]['id_attribute']) && $product_for_template['attributes'][$row['id_attribute_group']]['id_attribute'] == $row['id_attribute']) ? true : false;
                if (isset($shown_color_0)) {
                    $product_attribute_name = strtolower(Tools::translate($row['attribute_name']));
                    if ($shown_color_0 == $product_attribute_name) {
                        $selected = true;
                        $this->check_image_id = $variant_id_image;
                    } else {
                        $selected = false;
                    }
                }
                //将属性加入属性分组
                if ($this->product->supplier_reference == 'GLPD') {
                    // 供应商为GLPD时，只显示属性价格 --- 补差价产品
                    $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['name'] = Tools::displayPrice(Tools::convertPrice($row['attribute_price']));
                } else {
                    if ($row['attribute_price'] > 0) {
                        $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['name'] = Tools::translate($row['attribute_name']) . '+' . Tools::displayPrice(Tools::convertPrice($row['attribute_price']));
                    } else {
                        $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['name'] = Tools::translate($row['attribute_name']);
                    }
                }
                $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['id'] = $row['id_attribute'];
                $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['attribute_price_amount'] = round(Tools::convertPrice($row['attribute_price']), 2);
                $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['html_color_code'] = $row['attribute_color'];
                $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['code'] = strtolower($row['code']);
                $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['is_custom_size'] = $row['is_custom_size'];
                $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['texture'] = @filemtime(_PS_COL_IMG_DIR_ . $row['id_attribute'] . '.jpg') ? _THEME_COL_DIR_ . $row['id_attribute'] . '.jpg' : '';
                $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['selected'] = $selected;
                $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['attribute_color_code'] = strtolower($row['code']);
                // 颜色属性对应上的变体图片
                // 确保$variant_id_image不是null
                if (!isset($groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['product_attribute_image_id']) && $variant_id_image) {
                    $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['product_attribute_image_id'] = $variant_id_image;
                }
                if (!isset($groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['product_attribute_image']) && $variant_image) {
                    $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['product_attribute_image'] = $variant_image;
                    if ($selected && $row['is_color_group'] == 1) {
                        $shown_color_image = $variant_image_home;
                    }
                }
                // 色卡图片
                if (!isset($groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['material_color_image']) && $row['material_color_image']) {
                    $groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']]['material_color_image'] = 'https://' . _PS_IMAGE_DOMAIN_ . '/media/catalog/' . $row['material_color_image'];
                }
                // 默认选中的属性
                if ($row['default_on'] && $groups[$row['id_attribute_group']]['default'] == -1) {
                    $groups[$row['id_attribute_group']]['default'] = (int) $row['id_attribute'];
                }
                // 属性出现次数
                if (!isset($groups[$row['id_attribute_group']]['attributes_quantity'][$row['id_attribute']])) {
                    $groups[$row['id_attribute_group']]['attributes_quantity'][$row['id_attribute']] = 0;
                }
                $groups[$row['id_attribute_group']]['attributes_quantity'][$row['id_attribute']] += (int) $row['quantity'];
                //变体信息
                $combinations[$row['id_product_attribute']]['attributes_values'][$row['id_attribute_group']] = Tools::translate($row['attribute_name']);
                $combinations[$row['id_product_attribute']]['attributes'][] = (int) $row['id_attribute'];
                $combinations[$row['id_product_attribute']]['price'] = (float) $row['price'];

                //调用getPriceStatic方法来设置变体的特殊金额
                if (!isset($combination_prices_set[(int) $row['id_product_attribute']])) {
                    $combination_specific_price = null;
                    Product::getPriceStatic((int) $this->product->id, false, $row['id_product_attribute'], 6, null, false, true, 1, false, null, null, null, $combination_specific_price);
                    $combination_prices_set[(int) $row['id_product_attribute']] = true;
                    $combinations[$row['id_product_attribute']]['specific_price'] = $combination_specific_price;
                }
                $combinations[$row['id_product_attribute']]['ecotax'] = (float) $row['ecotax'];
                $combinations[$row['id_product_attribute']]['weight'] = (float) $row['weight'];
                $combinations[$row['id_product_attribute']]['quantity'] = (int) $row['quantity'];
                $combinations[$row['id_product_attribute']]['reference'] = $row['reference'];
                $combinations[$row['id_product_attribute']]['ean13'] = $row['ean13'];
                $combinations[$row['id_product_attribute']]['mpn'] = $row['mpn'];
                $combinations[$row['id_product_attribute']]['upc'] = $row['upc'];
                $combinations[$row['id_product_attribute']]['isbn'] = $row['isbn'];
                $combinations[$row['id_product_attribute']]['unit_impact'] = $row['unit_price_impact'];
                $combinations[$row['id_product_attribute']]['minimal_quantity'] = $row['minimal_quantity'];
                if ($row['available_date'] != '0000-00-00' && Validate::isDate($row['available_date'])) {
                    $combinations[$row['id_product_attribute']]['available_date'] = $row['available_date'];
                    $combinations[$row['id_product_attribute']]['date_formatted'] = Tools::displayDate($row['available_date']);
                } else {
                    $combinations[$row['id_product_attribute']]['available_date'] = $combinations[$row['id_product_attribute']]['date_formatted'] = '';
                }
                $combinations[$row['id_product_attribute']]['available_now'] = $row['available_now'];
                $combinations[$row['id_product_attribute']]['available_later'] = $row['available_later'];
                // 变体图片
                if (!isset($combination_images[$row['id_product_attribute']][0]['id_image'])) {
                    $combinations[$row['id_product_attribute']]['id_image'] = -1;
                } else {
                    $combinations[$row['id_product_attribute']]['id_image'] = (int) $combination_images[$row['id_product_attribute']][0]['id_image'];
                }
            }

            // wash attributes list depending on available attributes depending on selected preceding attributes
            $current_selected_attributes = [];
            $count = 0;
            $attribute_count = [];
            foreach ($groups as $k => &$group) {
                if ($group['group_type'] == 'size' || $group['group_type'] == 'color') {
                    $attribute_count[$k] = count($group['attributes']) . ' ' . $group['name'] . 's';
                }
                ++$count;
                if ($count > 1) {
                    //find attributes of current group, having a possible combination with current selected
                    $id_product_attributes = [0];
                    $query = 'SELECT pac.`id_product_attribute`
                        FROM `' . _DB_PREFIX_ . 'product_attribute_combination` pac
                        INNER JOIN `' . _DB_PREFIX_ . 'product_attribute` pa ON (pa.id_product_attribute = pac.id_product_attribute AND pa.`active` = 1)
                        WHERE id_product = ' . $this->product->id . ' AND id_attribute IN (' . implode(',', array_map('intval', $current_selected_attributes)) . ')
                        GROUP BY id_product_attribute
                        HAVING COUNT(id_product) = ' . count($current_selected_attributes);
                    if ($results = Db::getInstance()->executeS($query)) {
                        foreach ($results as $row) {
                            $id_product_attributes[] = $row['id_product_attribute'];
                        }
                    }
                    $id_attributes = Db::getInstance()->executeS('SELECT pac2.`id_attribute` FROM `' . _DB_PREFIX_ . 'product_attribute_combination` pac2' .
                        ((!Product::isAvailableWhenOutOfStock($this->product->out_of_stock) && 0 == Configuration::get('PS_DISP_UNAVAILABLE_ATTR')) ?
                            ' INNER JOIN `' . _DB_PREFIX_ . 'stock_available` pa ON pa.id_product_attribute = pac2.id_product_attribute
                        WHERE pa.quantity > 0 AND ' :
                            ' WHERE ') .
                        'pac2.`id_product_attribute` IN (' . implode(',', array_map('intval', $id_product_attributes)) . ')
                        AND pac2.id_attribute NOT IN (' . implode(',', array_map('intval', $current_selected_attributes)) . ')');
                    foreach ($id_attributes as $k => $row) {
                        $id_attributes[$k] = (int) $row['id_attribute'];
                    }
                    foreach ($group['attributes'] as $key => $attribute) {
                        if (!in_array((int) $key, $id_attributes)) {
                            unset(
                                $group['attributes'][$key],
                                $group['attributes_quantity'][$key]
                            );
                        }
                    }
                }
                //find selected attribute or first of group
                $index = 0;
                $current_selected_attribute = 0;
                foreach ($group['attributes'] as $key => $attribute) {
                    if ($index === 0) {
                        $current_selected_attribute = $key;
                    }
                    if ($attribute['selected']) {
                        $current_selected_attribute = $key;

                        break;
                    }
                }
                if ($current_selected_attribute > 0) {
                    $current_selected_attributes[] = $current_selected_attribute;
                }
            }

            // 去掉不可用的属性
            if (!Product::isAvailableWhenOutOfStock($this->product->out_of_stock) && Configuration::get('PS_DISP_UNAVAILABLE_ATTR') == 0) {
                foreach ($groups as &$group) {
                    foreach ($group['attributes_quantity'] as $key => $quantity) {
                        if ($quantity <= 0) {
                            unset($group['attributes'][$key]);
                        }
                    }
                }
                unset($group);
                foreach ($colors as $key => $color) {
                    if ($color['attributes_quantity'] <= 0) {
                        unset($colors[$key]);
                    }
                }
            }
            foreach ($combinations as $id_product_attribute => $comb) {
                $attribute_list = '';
                foreach ($comb['attributes'] as $id_attribute) {
                    $attribute_list .= '\'' . (int) $id_attribute . '\',';
                }
                $attribute_list = rtrim($attribute_list, ',');
                $combinations[$id_product_attribute]['list'] = $attribute_list;
            }
            return [
                'groups' => $groups,
                'colors' => (count($colors)) ? $colors : false,
                'combinations' => $combinations,
                'combinationImages' => $combination_images,
                'attribute_count' => implode(', ', $attribute_count),
                'shown_color_image' => $shown_color_image
            ];
        } else {
            return [
                'groups' => [],
                'colors' => false,
                'combinations' => [],
                'combinationImages' => [],
                'attribute_count' => '',
                'shown_color_image' => $shown_color_image
            ];
        }

    }

    /**
     * 获取变体，并且传到前台
     */
    protected function assignAttributesCombinations()
    {
        $sql = 'SELECT pha.id_attribute,pha.id_attribute_group, al.`name` as attribute,agl.`name` as `group`
            FROM ' . _DB_PREFIX_ . 'product_has_attribute pha
            left join ' . _DB_PREFIX_ . 'attribute_group_lang agl on pha.id_attribute_group=agl.id_attribute_group
            left join ' . _DB_PREFIX_ . 'attribute_lang al on pha.id_attribute=al.id_attribute
            where id_product=' . $this->product->id;
        $product_attributes = Db::getInstance()->executeS($sql);
        $attributes_combinations = [];
        if ($product_attributes) {
            foreach ($product_attributes as $product_attribute) {
                $attribute = str_replace(Configuration::get('PS_ATTRIBUTE_ANCHOR_SEPARATOR'), '_', Tools::str2url(str_replace([',', '.'], '-', $product_attribute['attribute'])));
                $group = str_replace(Configuration::get('PS_ATTRIBUTE_ANCHOR_SEPARATOR'), '_', Tools::str2url(str_replace([',', '.'], '-', $product_attribute['group'])));
                $attributes_combinations[] = [
                    'id_attribute' => $product_attribute['id_attribute'],
                    'id_attribute_group' => $product_attribute['id_attribute_group'],
                    'attribute' => $attribute,
                    'group' => $group,
                    'reference' => '',
                    'ean13' => '',
                    'isbn' => '',
                    'upc' => '',
                    'mpn' => '',
                ];
            }
        }
        $this->context->smarty->assign([
            'attributesCombinations' => $attributes_combinations,
            'attribute_anchor_separator' => Configuration::get('PS_ATTRIBUTE_ANCHOR_SEPARATOR'),
        ]);
    }

    /**
     * 传递产品所属的分类到前端
     */
    protected function assignCategory()
    {
        // Assign category to the template
        if (
            (empty($this->category) || !Validate::isLoadedObject($this->category) || !$this->category->inShop() || !$this->category->isAssociatedToShop())
            && Category::inShopStatic($this->product->id_category_default, $this->context->shop)
        ) {
            $this->category = new Category((int) $this->product->id_category_default, (int) $this->context->language->id);
        }

        $sub_categories = [];
        if (Validate::isLoadedObject($this->category)) {
            $sub_categories = $this->category->getSubCategories($this->context->language->id, true);

            //获取分类集
            $google_category = $this->category->id_parent . '/' . $this->category->id;
            $categoryIds = explode('/', $google_category);
            $categories = array();
            foreach ($categoryIds as $categoryId) {
                if ($categoryId == 1 || $categoryId == 2)
                    continue;
                $id_lang = (int) Context::getContext()->language->id;
                $categorysName = new Category($categoryId, $id_lang);
                $categories[] = $categorysName->name;
            }

            // 将分类ID数组存储在Cookie中以便最热产品获取分类id
            $this->query_category_ids = [(int) $this->product->id_category_default];
            $this->context->cookie->id_categories = json_encode($this->query_category_ids);

            $Keys = array('first_cat', 'sub_cat', 'thd_cat');
            $newKeys = array_slice($Keys, 0, count($categories));
            $newValues = array_combine($newKeys, $categories);
            $product_category = json_encode($newValues);

            // various assignements before Hook::exec
            $this->context->smarty->assign([
                'category' => $this->category,
                'subCategories' => $sub_categories,
                'subcategories' => $sub_categories,
                'id_category_current' => (int) $this->category->id,
                'id_category_parent' => (int) $this->category->id_parent,
                'return_category_name' => Tools::safeOutput($this->category->getFieldByLang('name')),
                'categories' => Category::getHomeCategories($this->context->language->id, true, (int) $this->context->shop->id),
                'google_category' => addslashes($product_category),
            ]);
        }
    }

    /**
     * 礼服的尺寸表(所有), 手机端尺寸右侧链接展示数据
     */
    protected function assignAllSizeChart()
    {
        $redis = Tools::getRedis();
        if ($redis->get(_PS_REDIS_CLOTHING_SIZE_CHART_)) {
            $dress_size_chart = json_decode($redis->get(_PS_REDIS_CLOTHING_SIZE_CHART_), true);
        } else {
            // 婚纱尺寸
            $dress_size = Db::getInstance()->executeS('SELECT * FROM `' . _DB_PREFIX_ . 'dress_size_chart`');
            $dress_size_chart = [];
            foreach ($dress_size as $d_size) {
                if ($d_size['unit_type'] == 1) {
                    $dress_size_chart['inch'][] = [
                        'us' => $d_size['us'],
                        'eu' => $d_size['eu'],
                        'uk' => $d_size['uk'],
                        'bust' => $d_size['bust'],
                        'waist' => $d_size['waist'],
                        'hips' => $d_size['hips'],
                        'hollow_to_floor' => $d_size['hollow_to_floor']
                    ];
                } elseif ($d_size['unit_type'] == 2) {
                    $dress_size_chart['cm'][] = [
                        'us' => $d_size['us'],
                        'eu' => $d_size['eu'],
                        'uk' => $d_size['uk'],
                        'bust' => $d_size['bust'],
                        'waist' => $d_size['waist'],
                        'hips' => $d_size['hips'],
                        'hollow_to_floor' => $d_size['hollow_to_floor']
                    ];
                }
            }
            $redis->set(_PS_REDIS_CLOTHING_SIZE_CHART_, json_encode($dress_size_chart), "EX", 86400);
        }

        $this->context->smarty->assign([
            'dress_size_chart' => $dress_size_chart,
            'size_image_path' => 'https://' . _PS_IMAGE_DOMAIN_ . '/media/catalog/resources/static/guide_weddingdress-large.png'
        ]);
    }

    /**
     * 处理产品描述中的图片
     * @param mixed $desc
     * @return mixed
     */
    protected function transformDescriptionWithImg($desc)
    {
        $reg = '/\[img\-([0-9]+)\-(left|right)\-([a-zA-Z0-9-_]+)\]/';
        while (preg_match($reg, $desc, $matches)) {
            $link_lmg = $this->context->link->getImageLink($this->product->link_rewrite, $this->product->id . '-' . $matches[1], $matches[3]);
            $class = $matches[2] == 'left' ? 'class="imageFloatLeft"' : 'class="imageFloatRight"';
            $html_img = '<img src="' . $link_lmg . '" alt="" ' . $class . '/>';
            $desc = str_replace($matches[0], $html_img, $desc);
        }
        return $desc;
    }

    /**
     * 产品原生自定义属性中的文件上传
     * @return bool
     */
    protected function pictureUpload()
    {
        if (!$field_ids = $this->product->getCustomizationFieldIds()) {
            return false;
        }
        $authorized_file_fields = [];
        foreach ($field_ids as $field_id) {
            if ($field_id['type'] == Product::CUSTOMIZE_FILE) {
                $authorized_file_fields[(int) $field_id['id_customization_field']] = 'file' . (int) $field_id['id_customization_field'];
            }
        }
        $indexes = array_flip($authorized_file_fields);
        foreach ($_FILES as $field_name => $file) {
            if (in_array($field_name, $authorized_file_fields) && isset($file['tmp_name']) && !empty($file['tmp_name'])) {
                $file_name = md5(uniqid((string) mt_rand(0, mt_getrandmax()), true));
                if ($error = ImageManager::validateUpload($file, (int) Configuration::get('PS_PRODUCT_PICTURE_MAX_SIZE'))) {
                    $this->errors[] = $error;
                }

                $product_picture_width = (int) Configuration::get('PS_PRODUCT_PICTURE_WIDTH');
                $product_picture_height = (int) Configuration::get('PS_PRODUCT_PICTURE_HEIGHT');
                $tmp_name = tempnam(_PS_TMP_IMG_DIR_, 'PS');
                if ($error || (!$tmp_name || !move_uploaded_file($file['tmp_name'], $tmp_name))) {
                    return false;
                }
                /* Original file */
                if (!ImageManager::resize($tmp_name, _PS_UPLOAD_DIR_ . $file_name)) {
                    $this->errors[] = $this->trans('An error occurred during the image upload process.', [], 'Shop.Notifications.Error');
                } elseif (!ImageManager::resize($tmp_name, _PS_UPLOAD_DIR_ . $file_name . '_small', $product_picture_width, $product_picture_height)) {
                    $this->errors[] = $this->trans('An error occurred during the image upload process.', [], 'Shop.Notifications.Error');
                } else {
                    $this->context->cart->addPictureToProduct($this->product->id, $indexes[$field_name], Product::CUSTOMIZE_FILE, $file_name);
                }
                unlink($tmp_name);
            }
        }

        return true;
    }

    /**
     * 产品原生自定义属性中的文字记录
     * @return bool
     */
    protected function textRecord()
    {
        if (!$field_ids = $this->product->getCustomizationFieldIds()) {
            return false;
        }

        $authorized_text_fields = [];
        foreach ($field_ids as $field_id) {
            if ($field_id['type'] == Product::CUSTOMIZE_TEXTFIELD) {
                $authorized_text_fields[(int) $field_id['id_customization_field']] = 'textField' . (int) $field_id['id_customization_field'];
            }
        }

        $indexes = array_flip($authorized_text_fields);
        foreach ($_POST as $field_name => $value) {
            if (in_array($field_name, $authorized_text_fields) && $value != '') {
                if (!Validate::isMessage($value)) {
                    $this->errors[] = $this->trans('Invalid message.', [], 'Shop.Notifications.Error');
                } else {
                    $this->context->cart->addTextFieldToProduct($this->product->id, $indexes[$field_name], Product::CUSTOMIZE_TEXTFIELD, $value);
                }
            } elseif (in_array($field_name, $authorized_text_fields) && $value == '') {
                $this->context->cart->deleteCustomizationToProduct((int) $this->product->id, $indexes[$field_name]);
            }
        }
    }

    /**
     * Calculation of currency-converted discounts for specific prices on product.
     *
     * @param array $specific_prices array of specific prices definitions (DEFAULT currency)
     * @param float $price current price in CURRENT currency
     * @param float $tax_rate in percents
     * @param float $ecotax_amount in DEFAULT currency, with tax
     *
     * @return array
     */
    protected function formatQuantityDiscounts($specific_prices, $price, $tax_rate, $ecotax_amount)
    {
        $priceCalculationMethod = Group::getPriceDisplayMethod(Group::getCurrent()->id);
        $isTaxIncluded = false;

        if ($priceCalculationMethod !== null && (int) $priceCalculationMethod === PS_TAX_INC) {
            $isTaxIncluded = true;
        }

        foreach ($specific_prices as $key => &$row) {
            $specificPriceFormatter = new SpecificPriceFormatter(
                $row,
                $isTaxIncluded,
                $this->context->currency,
                Configuration::get('PS_DISPLAY_DISCOUNT_PRICE')
            );
            $row = $specificPriceFormatter->formatSpecificPrice($price, $tax_rate, $ecotax_amount);
            $row['nextQuantity'] = (isset($specific_prices[$key + 1]) ? (int) $specific_prices[$key + 1]['from_quantity'] : -1);
        }

        return $specific_prices;
    }

    /**
     * @return Product
     */
    public function getProduct()
    {
        return $this->product;
    }

    /**
     * @return Category|null
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * 通过请求的id_product_attribute获取id_product_attribute
     *
     * @return int
     */
    protected function getIdProductAttributeByRequest()
    {
        $requestedIdProductAttribute = (int) Tools::getValue('id_product_attribute');

        return $this->tryToGetAvailableIdProductAttribute($requestedIdProductAttribute);
    }

    /**
     * 优先根据请求的属性ID数组、然后是请求的变体ID、然后是产品默认的变体ID来获取变体ID
     *
     * @return int|null
     *
     * @throws PrestaShopException
     */
    private function getIdProductAttributeByGroupOrRequestOrDefault()
    {
        // If the product has no combinations, we return early
        if (!$this->product->hasCombinations()) {
            return null;
        }

        //根据请求的属性ID数组获取变体ID
        $idProductAttribute = $this->getIdProductAttributeByGroup();

        //获取请求的变体ID
        if (null === $idProductAttribute) {
            $idProductAttribute = (int) Tools::getValue('id_product_attribute');
        }

        //获取默认变体ID
        if (0 === $idProductAttribute) {
            $idProductAttribute = (int) Product::getDefaultAttribute($this->product->id);
        }
        //验证变体是否可用
        return $this->tryToGetAvailableIdProductAttribute($idProductAttribute);
    }

    /**
     * 验证变体是否可用
     *
     * @param int $checkedIdProductAttribute
     *
     * @return int
     */
    protected function tryToGetAvailableIdProductAttribute($checkedIdProductAttribute)
    {
        if (!Configuration::get('PS_DISP_UNAVAILABLE_ATTR')) {
            $productCombinations = $this->product->getAttributeCombinations();
            if (!Product::isAvailableWhenOutOfStock($this->product->out_of_stock)) {
                $availableProductAttributes = array_filter(
                    $productCombinations,
                    function ($elem) {
                        return $elem['quantity'] > 0;
                    }
                );
            } else {
                $availableProductAttributes = $productCombinations;
            }

            $availableProductAttribute = array_filter(
                $availableProductAttributes,
                function ($elem) use ($checkedIdProductAttribute) {
                    return $elem['id_product_attribute'] == $checkedIdProductAttribute;
                }
            );

            if (empty($availableProductAttribute) && count($availableProductAttributes)) {
                // if selected combination is NOT available ($availableProductAttribute) but they are other alternatives ($availableProductAttributes), then we'll try to get the closest.
                if (!Product::isAvailableWhenOutOfStock($this->product->out_of_stock)) {
                    // first lets get information of the selected combination.
                    $checkProductAttribute = array_filter(
                        $productCombinations,
                        function ($elem) use ($checkedIdProductAttribute) {
                            return $elem['id_product_attribute'] == $checkedIdProductAttribute || (!$checkedIdProductAttribute && $elem['default_on']);
                        }
                    );
                    if (count($checkProductAttribute)) {
                        // now lets find other combinations for the selected attributes.
                        $alternativeProductAttribute = [];
                        foreach ($checkProductAttribute as $key => $attribute) {
                            $alternativeAttribute = array_filter(
                                $availableProductAttributes,
                                function ($elem) use ($attribute) {
                                    return $elem['id_attribute'] == $attribute['id_attribute'] && !$elem['is_color_group'];
                                }
                            );
                            foreach ($alternativeAttribute as $key => $value) {
                                $alternativeProductAttribute[$key] = $value;
                            }
                        }

                        if (count($alternativeProductAttribute)) {
                            // if alternative combination is found, order the list by quantity to use the one with more stock.
                            usort($alternativeProductAttribute, function ($a, $b) {
                                if ($a['quantity'] == $b['quantity']) {
                                    return 0;
                                }

                                return ($a['quantity'] > $b['quantity']) ? -1 : 1;
                            });

                            return (int) array_shift($alternativeProductAttribute)['id_product_attribute'];
                        }
                    }
                }

                return (int) array_shift($availableProductAttributes)['id_product_attribute'];
            }
        }

        return $checkedIdProductAttribute;
    }

    /**
     * 根据产品的属性来获取变体ID
     * group是id_attribute的数组
     *
     * @return int|null
     *
     * @throws PrestaShopException
     */
    private function getIdProductAttributeByGroup()
    {
        try {
            $groups = Tools::getValue('group');
            if (empty($groups)) {
                return null;
            }
            //根据产品的属性来获取变体ID
            return (int) Product::getIdProductAttributeByIdAttributes(
                $this->product->id,
                $groups,
                true
            );
        } catch (Exception $e) {
            PrestaShopLogger::addLog(
                'Error: ' . $e->getMessage(),
                1,
                $e->getCode(),
                'Product'
            );
        }

        return 0;
    }

    // 插叙尺寸表三条数据的所有信息
    private function getSizeChart()
    {
        $size_charts = [];
        // 婚纱尺寸表
        $dress_size_charts = Db::getInstance()->executeS('SELECT * FROM `' . _DB_PREFIX_ . 'dress_size_chart`');
        if (!empty($dress_size_charts)) {
            $size_charts = array_merge($size_charts, $this->getTemplateSizeChart($dress_size_charts));
        }

        // 花童尺寸表
        $flower_girl_dress_size_charts = Db::getInstance()->executeS('SELECT * FROM `' . _DB_PREFIX_ . 'flower_girl_dress_size_chart`');
        if (!empty($flower_girl_dress_size_charts)) {
            $size_charts = array_merge($size_charts, $this->getTemplateSizeChart($flower_girl_dress_size_charts));
        }

        // 伴娘尺寸表
        $junior_bridesmaid_dresses_size_charts = Db::getInstance()->executeS('SELECT * FROM `' . _DB_PREFIX_ . 'junior_bridesmaid_dresses_size_chart`');
        if (!empty($junior_bridesmaid_dresses_size_charts)) {
            $size_charts = array_merge($size_charts, $this->getTemplateSizeChart($junior_bridesmaid_dresses_size_charts));
        }
        return [
            'size_charts' => $size_charts,
            'custom_size_image_url' => 'https://' . _PS_IMAGE_DOMAIN_ . '/media/catalog/public/custom_size/guide_weddingdress.jpg',
            'custom_size_image_large_url' => 'https://' . _PS_IMAGE_DOMAIN_ . '/media/catalog/public/custom_size/guide_weddingdress-large.png'
        ];
    }

    /**
     * Summary of getTemplateSizeChart
     * @param mixed $data
     * @return array
     */
    public function getTemplateSizeChart($data)
    {
        $size_charts = [];
        // 整理数据
        $size_data = [];
        foreach ($data as $item) {
            if (!isset($size_data[strtolower($item['size_code'])])) {
                $size_data[strtolower($item['size_code'])]['id_size_code'] = $item['id_size_code'];
                $size_data[strtolower($item['size_code'])]['size_code'] = $item['size_code'];
            }
            if ($item['unit_type'] == 1) {
                $size_data[strtolower($item['size_code'])]['inch'] = [
                    'size' => $item['size'],
                    'bust' => $item['bust'],
                    'waist' => $item['waist'],
                    'hips' => $item['hips'],
                    'hollow_to_floor' => $item['hollow_to_floor'],
                ];
            } else {
                $size_data[strtolower($item['size_code'])]['cm'] = [
                    'size' => $item['size'],
                    'bust' => $item['bust'],
                    'waist' => $item['waist'],
                    'hips' => $item['hips'],
                    'hollow_to_floor' => $item['hollow_to_floor'],
                ];
            }
        }
        // 重置键值
        $size_data = array_values($size_data);

        // 配对数据
        if (count($size_data) > 3) {
            // 尺寸表数据超过3条，处理逻辑
            foreach ($size_data as $key => $value) {
                $inch_size_chart = [];
                $cm_size_chart = [];
                // 第一条，则保存当前循环的数据
                // inch
                $inch_size_chart[$size_data[$key]['id_size_code']] = [
                    'size' => $size_data[$key]['inch']['size'],
                    'bust' => $size_data[$key]['inch']['bust'],
                    'waist' => $size_data[$key]['inch']['waist'],
                    'hips' => $size_data[$key]['inch']['hips'],
                    'hollow_to_floor' => $size_data[$key]['inch']['hollow_to_floor'],
                    'selected' => true
                ];
                // cm
                $cm_size_chart[$size_data[$key]['id_size_code']] = [
                    'size' => $size_data[$key]['cm']['size'],
                    'bust' => $size_data[$key]['cm']['bust'],
                    'waist' => $size_data[$key]['cm']['waist'],
                    'hips' => $size_data[$key]['cm']['hips'],
                    'hollow_to_floor' => $size_data[$key]['cm']['hollow_to_floor'],
                    'selected' => true
                ];

                // 第二条数据
                if (isset($size_data[$key - 1])) {
                    // 前一条存在获取前一条数据
                    // inch
                    $inch_size_chart[$size_data[$key - 1]['id_size_code']] = [
                        'size' => $size_data[$key - 1]['inch']['size'],
                        'bust' => $size_data[$key - 1]['inch']['bust'],
                        'waist' => $size_data[$key - 1]['inch']['waist'],
                        'hips' => $size_data[$key - 1]['inch']['hips'],
                        'hollow_to_floor' => $size_data[$key - 1]['inch']['hollow_to_floor'],
                        'selected' => false
                    ];
                    // cm
                    $cm_size_chart[$size_data[$key - 1]['id_size_code']] = [
                        'size' => $size_data[$key - 1]['cm']['size'],
                        'bust' => $size_data[$key - 1]['cm']['bust'],
                        'waist' => $size_data[$key - 1]['cm']['waist'],
                        'hips' => $size_data[$key - 1]['cm']['hips'],
                        'hollow_to_floor' => $size_data[$key - 1]['cm']['hollow_to_floor'],
                        'selected' => false
                    ];
                } else {
                    // 前一条不存在获取后俩条数据
                    // inch
                    $inch_size_chart[$size_data[$key + 2]['id_size_code']] = [
                        'size' => $size_data[$key + 2]['inch']['size'],
                        'bust' => $size_data[$key + 2]['inch']['bust'],
                        'waist' => $size_data[$key + 2]['inch']['waist'],
                        'hips' => $size_data[$key + 2]['inch']['hips'],
                        'hollow_to_floor' => $size_data[$key + 2]['inch']['hollow_to_floor'],
                        'selected' => false
                    ];
                    // cm
                    $cm_size_chart[$size_data[$key + 2]['id_size_code']] = [
                        'size' => $size_data[$key + 2]['cm']['size'],
                        'bust' => $size_data[$key + 2]['cm']['bust'],
                        'waist' => $size_data[$key + 2]['cm']['waist'],
                        'hips' => $size_data[$key + 2]['cm']['hips'],
                        'hollow_to_floor' => $size_data[$key + 2]['cm']['hollow_to_floor'],
                        'selected' => false
                    ];
                }

                // 第三条数据
                if (isset($size_data[$key + 1])) {
                    // 后一条数据存在
                    // inch
                    $inch_size_chart[$size_data[$key + 1]['id_size_code']] = [
                        'size' => $size_data[$key + 1]['inch']['size'],
                        'bust' => $size_data[$key + 1]['inch']['bust'],
                        'waist' => $size_data[$key + 1]['inch']['waist'],
                        'hips' => $size_data[$key + 1]['inch']['hips'],
                        'hollow_to_floor' => $size_data[$key + 1]['inch']['hollow_to_floor'],
                        'selected' => false
                    ];
                    // cm
                    $cm_size_chart[$size_data[$key + 1]['id_size_code']] = [
                        'size' => $size_data[$key + 1]['cm']['size'],
                        'bust' => $size_data[$key + 1]['cm']['bust'],
                        'waist' => $size_data[$key + 1]['cm']['waist'],
                        'hips' => $size_data[$key + 1]['cm']['hips'],
                        'hollow_to_floor' => $size_data[$key + 1]['cm']['hollow_to_floor'],
                        'selected' => false
                    ];
                } else {
                    // 后一条数据不存在获取向前俩条的数据
                    // inch
                    $inch_size_chart[$size_data[$key - 2]['id_size_code']] = [
                        'size' => $size_data[$key - 2]['inch']['size'],
                        'bust' => $size_data[$key - 2]['inch']['bust'],
                        'waist' => $size_data[$key - 2]['inch']['waist'],
                        'hips' => $size_data[$key - 2]['inch']['hips'],
                        'hollow_to_floor' => $size_data[$key - 2]['inch']['hollow_to_floor'],
                        'selected' => false
                    ];
                    // cm
                    $cm_size_chart[$size_data[$key - 2]['id_size_code']] = [
                        'size' => $size_data[$key - 2]['cm']['size'],
                        'bust' => $size_data[$key - 2]['cm']['bust'],
                        'waist' => $size_data[$key - 2]['cm']['waist'],
                        'hips' => $size_data[$key - 2]['cm']['hips'],
                        'hollow_to_floor' => $size_data[$key - 2]['cm']['hollow_to_floor'],
                        'selected' => false
                    ];
                }

                // 排个序
                ksort($inch_size_chart);
                ksort($cm_size_chart);

                $size_charts[strtolower($value['size_code'])] = [
                    'inch' => $inch_size_chart,
                    'cm' => $cm_size_chart
                ];
            }
        } else {
            // 尺寸表数据不超过3条，处理逻辑
            foreach ($size_data as $value) {
                $inch_size_chart = [];
                $cm_size_chart = [];
                foreach ($size_data as $value2) {
                    $inch_size_chart[$value2['id_size_code']] = [
                        'bust' => $value2['inch']['bust'],
                        'waist' => $value2['inch']['waist'],
                        'hips' => $value2['inch']['hips'],
                        'hollow_to_floor' => $value2['inch']['hollow_to_floor'],
                        'selected' => $value2['id_size_code'] == $value['id_size_code'] ? true : false
                    ];

                    $cm_size_chart[$value2['id_size_code']] = [
                        'bust' => $value2['cm']['bust'],
                        'waist' => $value2['cm']['waist'],
                        'hips' => $value2['cm']['hips'],
                        'hollow_to_floor' => $value2['cm']['hollow_to_floor'],
                        'selected' => $value2['id_size_code'] == $value['id_size_code'] ? true : false
                    ];
                }
                $size_charts[strtolower($value['size_code'])] = [
                    'inch' => $inch_size_chart,
                    'cm' => $cm_size_chart
                ];
            }
        }

        return $size_charts;
    }

    /**
     * 传给模板的产品变量
     * @return \PrestaShop\PrestaShop\Adapter\Presenter\Product\ProductLazyArray
     */
    public function getTemplateVarProduct()
    {
        //产品解析器设置
        $productSettings = $this->getProductPresentationSettings();
        // Hook displayProductExtraContent
        $extraContentFinder = new ProductExtraContentFinder();
        //解析产品的字段
        $product = $this->objectPresenter->present($this->product);
        //产品ID
        $product['id_product'] = (int) $this->product->id;
        //是否在架
        $product['out_of_stock'] = (int) $this->product->out_of_stock;
        //是否新品
        $product['new'] = (int) $this->product->new;
        //产品当前选中的变体ID
        $product['id_product_attribute'] = $this->getIdProductAttributeByGroupOrRequestOrDefault();
        //最小数量
        $product['minimal_quantity'] = $this->getProductMinimalQuantity($product);
        //期望的数量
        $product['quantity_wanted'] = $this->getRequiredQuantity($product);
        //额外信息，有走HOOK
        $product['extraContent'] = $extraContentFinder->addParams(['product' => $this->product])->present();
        // 购物车产品ID
        $product['id_cart_product'] = Tools::getValue('id_cart_product');
        //产品信息
        $product_full = Product::getProductProperties($this->context->language->id, $product, $this->context);
        //添加产品原生自定义数据
        $product_full = $this->addProductCustomizationData($product_full);
        //是否展示库存
        $product_full['show_quantities'] = (bool) (
            Configuration::get('PS_DISPLAY_QTIES')
            && Configuration::get('PS_STOCK_MANAGEMENT')
            && $this->product->quantity > 0
            && $this->product->available_for_order
            && !Configuration::isCatalogMode()
        );
        // 基础价格
        $product_full['old_base_price'] = $this->product->base_price;
        //库存标签名称
        $product_full['quantity_label'] = ($this->product->quantity > 1) ? $this->trans('Items', [], 'Shop.Theme.Catalog') : $this->trans('Item', [], 'Shop.Theme.Catalog');
        //数量折扣
        $product_full['quantity_discounts'] = $this->quantity_discounts;
        //产品单价
        $product_full['unit_price'] = $productSettings->include_taxes ? $product_full['unit_price_tax_included'] : $product_full['unit_price_tax_excluded'];
        //产品的用户组折扣
        $group_reduction = GroupReduction::getValueForProduct($this->product->id, (int) Group::getCurrent()->id);
        if ($group_reduction === false) {
            $group_reduction = Group::getReduction((int) $this->context->cookie->id_customer) / 100;
        }
        $product_full['customer_group_discount'] = $group_reduction;
        //产品标题
        $product_full['title'] = $this->getProductPageTitle();
        //不带货币符号的价格
        $product_full['rounded_display_price'] = Tools::ps_round(
            $product_full['price'],
            Context::getContext()->currency->precision
        );
        $presenter = $this->getProductPresenter();
        /** @var \PrestaShop\PrestaShop\Adapter\Presenter\Product\ProductLazyArray */
        return $presenter->present(
            $productSettings,
            $product_full,
            $this->context->language
        );
    }

    // 格式化产品数据
    protected function formatProduct($data)
    {
        return [
            'id' => $data->id ?? null,
            'id_product' => $data->id ?? null,
            'id_shop_default' => $data->id_shop_default ?? 1,
            'id_manufacturer' => $data->id_manufacturer ?? null,
            'id_supplier' => $data->id_supplier ?? null,
            'reference' => $data->reference ?? '',
            'supplier_reference' => $data->supplier_reference ?? '',
            'video_url' => $data->video_url ?? '',
            'location' => $data->location ?? '',
            'width' => $data->width ?? '0.000000',
            'height' => $data->height ?? '0.000000',
            'depth' => $data->depth ?? '0.000000',
            'weight' => $data->weight ?? '0.000000',
            'quantity_discount' => $data->quantity_discount ?? '0',
            'ean13' => $data->ean13 ?? '',
            'isbn' => $data->isbn ?? '',
            'upc' => $data->upc ?? '',
            'mpn' => $data->mpn ?? '',
            'cache_is_pack' => $data->cache_is_pack ?? '0',
            'cache_has_attachments' => $data->cache_has_attachments ?? '0',
            'is_virtual' => $data->is_virtual ?? '0',
            'state' => $data->state ?? 1,
            'additional_delivery_times' => $data->additional_delivery_times ?? 1,
            'delivery_in_stock' => $data->delivery_in_stock ?? null,
            'delivery_out_stock' => $data->delivery_out_stock ?? null,
            'product_type' => $data->product_type ?? 'combinations',
            'id_material' => $data->id_material ?? null,
            'is_trailing' => $data->is_trailing ?? 1,
            'type' => $data->type ?? 'clothes',
            'current_price' => $data->current_price ?? '0.000000',
            'is_update_attribute' => $data->is_update_attribute ?? '0',
            'is_update' => $data->is_update ?? '1',
            'create_date' => $data->create_date ?? null,
            'is_ship_in_48hrs' => $data->is_ship_in_48hrs ?? '0',
            'attribute_set' => $data->attribute_set ?? null,
            'id_category_default' => $data->id_category_default ?? null,
            'id_tax_rules_group' => $data->id_tax_rules_group ?? 0,
            'on_sale' => $data->on_sale ?? '0',
            'online_only' => $data->online_only ?? '0',
            'ecotax' => $data->ecotax ?? '0.000000',
            'minimal_quantity' => $data->minimal_quantity ?? 1,
            'low_stock_threshold' => $data->low_stock_threshold ?? null,
            'low_stock_alert' => $data->low_stock_alert ?? '0',
            'price' => $data->price ?? '',
            'wholesale_price' => $data->wholesale_price ?? '0.000000',
            'unity' => $data->unity ?? null,
            'unit_price' => $data->unit_price ?? '',
            'unit_price_ratio' => $data->unit_price_ratio ?? 0.0,
            'additional_shipping_cost' => $data->additional_shipping_cost ?? '0.000000',
            'customizable' => $data->customizable ?? 0,
            'text_fields' => $data->text_fields ?? 0,
            'uploadable_files' => $data->uploadable_files ?? 0,
            'active' => $data->active ?? '1',
            'redirect_type' => $data->redirect_type ?? 'default',
            'id_type_redirected' => $data->id_type_redirected ?? 0,
            'available_for_order' => $data->available_for_order ?? '1',
            'available_date' => $data->available_date ?? null,
            'show_condition' => $data->show_condition ?? '1',
            'condition' => $data->condition ?? 'new',
            'show_price' => $data->show_price ?? '1',
            'indexed' => $data->indexed ?? '0',
            'visibility' => $data->visibility ?? 'both',
            'cache_default_attribute' => $data->cache_default_attribute ?? null,
            'advanced_stock_management' => $data->advanced_stock_management ?? '0',
            'date_add' => $data->date_add ?? null,
            'date_upd' => $data->date_upd ?? null,
            'pack_stock_type' => $data->pack_stock_type ?? 3,
            'meta_description' => $data->meta_description ?? '',
            'meta_keywords' => $data->meta_keywords ?? '',
            'meta_title' => $data->meta_title ?? '',
            'link_rewrite' => $data->link_rewrite ?? '',
            'name' => $data->name ?? '',
            'description' => $data->description ?? '',
            'description_short' => $data->description_short ?? '',
            'available_now' => $data->available_now ?? null,
            'available_later' => $data->available_later ?? null,
            'model_description' => $data->model_description ?? '',
            'out_of_stock' => $data->out_of_stock ?? 0,
            'new' => $data->new ?? 0,
            'extraContent' => $data->extraContent ?? [],
            'id_cart_product' => $data->id_cart_product ?? false,
            'id_product_attribute' => $data->id_product_attribute ?? null,
            'allow_oosp' => $data->allow_oosp ?? true,
            'cover_image_id' => $data->cover_image_id ?? null,
            'category' => $data->category ?? '',
            'category_name' => $data->category_name ?? '',
            'link' => $data->link ?? '',
            'manufacturer_name' => $data->manufacturer_name ?? null,
            'attribute_price' => $data->attribute_price ?? 0.0,
            'price_tax_exc' => $data->price_tax_exc ?? 0.0,
            'initial_original_price' => $data->initial_original_price ?? '0.000000',
            'price_without_reduction_without_tax' => $data->price_without_reduction_without_tax ?? 0.0,
            'price_without_reduction' => $data->price_without_reduction ?? 0.0,
            'reduction' => $data->reduction ?? 0.0,
            'reduction_without_tax' => $data->reduction_without_tax ?? 0.0,
            'specific_prices' => $data->specific_prices ?? [],
            'quantity' => $data->quantity ?? 0,
            'quantity_all_versions' => $data->quantity_all_versions ?? 0,
            'id_image' => $data->id_image ?? 'en-default',
            'features' => $data->features ?? [],
            'attachments' => $data->attachments ?? [],
            'virtual' => $data->virtual ?? 0,
            'pack' => $data->pack ?? 0,
            'packItems' => $data->packItems ?? [],
            'nopackprice' => $data->nopackprice ?? 0,
            'customization_required' => $data->customization_required ?? false,
            'attributes' => $data->attributes ?? [],
            'rate' => $data->rate ?? 0.0,
            'tax_name' => $data->tax_name ?? '',
            'ecotax_rate' => $data->ecotax_rate ?? 0.0,
            'unit_price_tax_excluded' => $data->unit_price_tax_excluded ?? 0.0,
            'unit_price_tax_included' => $data->unit_price_tax_included ?? 0.0,
            'show_quantities' => $data->show_quantities ?? false,
            'old_base_price' => $data->old_base_price ?? '0.000000',
            'quantity_label' => $data->quantity_label ?? 'Items',
            'quantity_discounts' => $data->quantity_discounts ?? null,
            'customer_group_discount' => $data->customer_group_discount ?? 0.0,
            'title' => $data->title ?? '',
            'rounded_display_price' => $data->rounded_display_price ?? 0.0,
            'images' => $data->images ?? [],
            'default_image' => $data->default_image ?? [],
            'back_cover' => $data->back_cover ?? [],
            'cover' => $data->cover ?? [],
            'has_discount' => $data->has_discount ?? false,
            'discount_type' => $data->discount_type ?? null,
            'discount_percentage' => $data->discount_percentage ?? null,
            'discount_percentage_absolute' => $data->discount_percentage_absolute ?? null,
            'discount_amount' => $data->discount_amount ?? null,
            'discount_amount_to_display' => $data->discount_amount_to_display ?? null,
            'price_amount' => $data->price_amount ?? 0.0,
            'regular_price_amount' => $data->regular_price_amount ?? 0.0,
            'regular_price' => $data->regular_price ?? '',
            'current_price_currency' => $data->current_price_currency ?? '',
            'regular_base_price' => $data->regular_base_price ?? '',
            'regular_base_price_amount' => $data->regular_base_price_amount ?? 0.0,
            'base_price' => $data->base_price ?? '',
            'base_price_amount' => $data->base_price_amount ?? 0.0,
            'discount_to_display' => $data->discount_to_display ?? null,
            'unit_price_full' => $data->unit_price_full ?? '',
            'show_availability' => $data->show_availability ?? false,
            'availability_message' => $data->availability_message ?? null,
            'availability_submessage' => $data->availability_submessage ?? null,
            'availability_date' => $data->availability_date ?? null,
            'availability' => $data->availability ?? null,
            'add_to_cart_url' => $data->add_to_cart_url ?? '',
            'url' => $data->url ?? '',
            'seo_availability' => $data->seo_availability ?? '',
            'weight_unit' => $data->weight_unit ?? '',
            'embedded_attributes' => $data->embedded_attributes ?? [],
            'grouped_features' => $data->grouped_features ?? [],
        ];
    }

    /**
     * 获取产品的最小数量
     * @param array $product
     *
     * @return int
     */
    protected function getProductMinimalQuantity($product)
    {
        $minimal_quantity = 1;
        if ($product['id_product_attribute']) {
            //根据变体ID获取属性
            $combination = $this->findProductCombinationById($product['id_product_attribute']);
            if ($combination && $combination['minimal_quantity']) {
                $minimal_quantity = $combination['minimal_quantity'];
            }
        } else {
            $minimal_quantity = $this->product->minimal_quantity;
        }
        return $minimal_quantity;
    }

    /**
     * 根据变体ID获取属性
     * @param int $combinationId
     *
     * @return ProductController|null
     */
    public function findProductCombinationById($combinationId)
    {
        $combinations = $this->product->getAttributesGroups($this->context->language->id, $combinationId);

        if (!is_array($combinations) || empty($combinations)) {
            return null;
        }

        return reset($combinations);
    }

    /**
     * 产品需要的最小数量
     * @param array $product
     *
     * @return int
     */
    protected function getRequiredQuantity($product)
    {
        $requiredQuantity = (int) Tools::getValue('quantity_wanted', $this->getProductMinimalQuantity($product));
        if ($requiredQuantity < $product['minimal_quantity']) {
            $requiredQuantity = $product['minimal_quantity'];
        }

        return $requiredQuantity;
    }

    /**
     * 获取面包屑链接
     * @return array
     */
    public function getBreadcrumbLinks()
    {
        $breadcrumb = parent::getBreadcrumbLinks();
        if (!$this->product) {
            return $breadcrumb;
        }
        $categoryDefault = new Category($this->product->id_category_default, $this->context->language->id);

        foreach ($categoryDefault->getAllParents() as $category) {
            /** @var Category $category */
            if ($category->id_parent != 0 && !$category->is_root_category && $category->active) {
                $breadcrumb['links'][] = [
                    'title' => $category->name,
                    'url' => $this->context->link->getCategoryLink($category),
                ];
            }
        }

        if ($categoryDefault->id_parent != 0 && !$categoryDefault->is_root_category && $categoryDefault->active) {
            $breadcrumb['links'][] = [
                'title' => $categoryDefault->name,
                'url' => $this->context->link->getCategoryLink($categoryDefault),
            ];
        }

        $breadcrumb['links'][] = [
            'title' => '#' . $this->product->supplier_reference,
            'url' => $this->context->link->getProductLink($this->product, null, null, null, null, null, (int) $this->getIdProductAttributeByRequest()),
        ];

        return $breadcrumb;
    }

    /**
     * 添加产品原生自定义数据
     */
    protected function addProductCustomizationData(array $product_full)
    {
        if ($product_full['customizable']) {
            $customizationData = [
                'fields' => [],
            ];

            $customized_data = [];

            $already_customized = $this->context->cart->getProductCustomization(
                $product_full['id_product'],
                null,
                true
            );

            $id_customization = 0;
            foreach ($already_customized as $customization) {
                $id_customization = $customization['id_customization'];
                $customized_data[$customization['index']] = $customization;
            }

            $customization_fields = $this->product->getCustomizationFields($this->context->language->id);
            if (is_array($customization_fields)) {
                foreach ($customization_fields as $customization_field) {
                    // 'id_customization_field' maps to what is called 'index'
                    // in what Product::getProductCustomization() returns
                    $key = $customization_field['id_customization_field'];

                    $field['label'] = $customization_field['name'];
                    $field['id_customization_field'] = $customization_field['id_customization_field'];
                    $field['required'] = $customization_field['required'];

                    switch ($customization_field['type']) {
                        case Product::CUSTOMIZE_FILE:
                            $field['type'] = 'image';
                            $field['image'] = null;
                            $field['input_name'] = 'file' . $customization_field['id_customization_field'];

                            break;
                        case Product::CUSTOMIZE_TEXTFIELD:
                            $field['type'] = 'text';
                            $field['text'] = '';
                            $field['input_name'] = 'textField' . $customization_field['id_customization_field'];

                            break;
                        default:
                            $field['type'] = null;
                    }

                    if (array_key_exists($key, $customized_data)) {
                        $data = $customized_data[$key];
                        $field['is_customized'] = true;
                        switch ($customization_field['type']) {
                            case Product::CUSTOMIZE_FILE:
                                $imageRetriever = new ImageRetriever($this->context->link);
                                $field['image'] = $imageRetriever->getCustomizationImage(
                                    $data['value']
                                );
                                $field['remove_image_url'] = $this->context->link->getProductDeletePictureLink(
                                    $product_full,
                                    $customization_field['id_customization_field']
                                );

                                break;
                            case Product::CUSTOMIZE_TEXTFIELD:
                                $field['text'] = $data['value'];

                                break;
                        }
                    } else {
                        $field['is_customized'] = false;
                    }

                    $customizationData['fields'][] = $field;
                }
            }
            $product_full['customizations'] = $customizationData;
            $product_full['id_customization'] = $id_customization;
            $product_full['is_customizable'] = true;
        } else {
            $product_full['customizations'] = [
                'fields' => [],
            ];
            $product_full['id_customization'] = 0;
            $product_full['is_customizable'] = false;
        }

        return $product_full;
    }

    /**
     * @return array
     */
    public function getTemplateVarPage()
    {
        $page = parent::getTemplateVarPage();

        if (
            Tools::isSubmit('pid')
            && Tools::isSubmit('cid')
            && (Tools::isSubmit('fbclid')
                || Tools::isSubmit('gclid')
                || Tools::isSubmit('dclid')
                || Tools::isSubmit('ttclid')
                || Tools::isSubmit('msclkid')
                || Tools::isSubmit('epik'))
        ) {
            $page['page_name'] = 'category';
            $page['body_classes']['category-id-' . $this->category->id] = true;
            $page['body_classes']['category-' . $this->category->name] = true;
            $page['body_classes']['category-id-parent-' . $this->category->id_parent] = true;
            $page['body_classes']['category-depth-level-' . $this->category->level_depth] = true;
        } else {
            if (!Validate::isLoadedObject($this->product)) {
                $page['title'] = $this->trans('The page you are looking for was not found.', [], 'Shop.Theme.Global');
                $page['page_name'] = 'pagenotfound';
                return $page;
            }

            $page['body_classes']['product-id-' . $this->product->id] = true;
            $page['body_classes']['product-' . $this->product->name] = true;
            $page['body_classes']['product-id-category-' . $this->product->id_category_default] = true;
            $page['body_classes']['product-id-manufacturer-' . $this->product->id_manufacturer] = true;
            $page['body_classes']['product-id-supplier-' . $this->product->id_supplier] = true;

            if ($this->product->on_sale) {
                $page['body_classes']['product-on-sale'] = true;
            }

            if ($this->product->available_for_order) {
                $page['body_classes']['product-available-for-order'] = true;
            }

            if ($this->product->customizable) {
                $page['body_classes']['product-customizable'] = true;
            }
            $page['admin_notifications'] = array_merge($page['admin_notifications'], $this->adminNotifications);
            $page['meta']['title'] = $this->getProductPageTitle($page['meta']);

        }
        return $page;
    }

    /**
     * 经常一起购买
     * @return array
     */
    public function getAccessories()
    {
        $category_ids = $this->product->getCategories();
        foreach ($category_ids as $key => $id_category) {
            if ($id_category <= 2) {
                unset($category_ids[$key]);
            }
        }
        asort($category_ids);
        $first_category = array_shift($category_ids);
        $accessory_product_str = Db::getInstance()->getValue(
            'SELECT recommend_product_sku FROM ' . _DB_PREFIX_ . 'category WHERE id_category = ' . (int) $first_category
        );
        $accessory_product_skus = explode(',', $accessory_product_str);
        if ($accessory_product_str && $accessory_product_skus) {
            $idLang = $this->context->language->id;
            $products = Db::getInstance()->executeS(
                'SELECT p.id_product FROM `' . _DB_PREFIX_ . 'product` p ' . Shop::addSqlAssociation('product', 'p') .
                ' LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (p.`id_product` = pl.`id_product` AND pl.`id_lang` = ' . (int) $idLang . Shop::addSqlRestrictionOnLang('pl') . ')
                WHERE p.`supplier_reference` IN (\'' . implode('\',\'', $accessory_product_skus) . '\')'
            );
            $products_for_template = [];

            if (is_array($products)) {
                $productIds = array_column($products, 'id_product');
                $products_for_template = Product::CompleteProductData($productIds);
            }
            return $products_for_template;
        }
        return [];
    }

    /**
     * 获取产品标题
     * @param array|null $meta
     *
     * @return string
     */
    private function getProductPageTitle(array $meta = null)
    {
        $title = $this->product->name;
        if (isset($meta['title'])) {
            $title = $meta['title'];
        } elseif (isset($meta['meta_title'])) {
            $title = $meta['meta_title'];
        }
        if (!Configuration::get('PS_PRODUCT_ATTRIBUTES_IN_TITLE')) {
            return $title;
        }

        $idProductAttribute = $this->getIdProductAttributeByGroupOrRequestOrDefault();
        if ($idProductAttribute) {
            $attributes = $this->product->getAttributeCombinationsById($idProductAttribute, $this->context->language->id);
            if (is_array($attributes) && count($attributes) > 0) {
                foreach ($attributes as $attribute) {
                    $title .= ' ' . $attribute['group_name'] . ' ' . $attribute['attribute_name'];
                }
            }
        }

        return $title;
    }

    /**
     * {@inheritdoc}
     *
     * 验证属性是否正确
     *
     * @param int $productAttributeId
     * @param int $productId
     *
     * @return bool
     */
    protected function isValidCombination($productAttributeId, $productId)
    {
        if ($productAttributeId > 0 && $productId > 0) {
            $combination = new Combination($productAttributeId);

            return
                Validate::isLoadedObject($combination)
                && $combination->id_product == $productId;
        }

        return false;
    }

    /**
     * Return information whether we are or not in quick view mode.
     *
     * @return bool
     */
    public function isQuickView(): bool
    {
        return $this->isQuickView;
    }

    /**
     * Set quick view mode.
     *
     * @param bool $enabled
     */
    public function setQuickViewMode(bool $enabled = true)
    {
        $this->isQuickView = $enabled;
    }

    /**
     * Return information whether we are or not in preview mode.
     *
     * @return bool
     */
    public function isPreview(): bool
    {
        return $this->isPreview;
    }

    /**
     * Set preview mode.
     *
     * @param bool $enabled
     */
    public function setPreviewMode(bool $enabled = true)
    {
        $this->isPreview = $enabled;
    }

    // 引入css js
    public function setMedia()
    {
        $this->registerJavascript('theme-pages-product', '/assets/js/pages/product.js', ['position' => 'bottom', 'priority' => 2000, 'version' => 301]);
        $this->registerJavascript('theme-pages-bootstrap', '/assets/js/pages/bootstrap.bundle.min.js', ['position' => 'bottom', 'priority' => 1999]);
        parent::setMedia();
    }

    /**
     * 特殊链接，展示分类页信息，并将指定产品加到分类页第一个
     * @return never
     */
    public function displayCategory()
    {
        $id_category = (int) Tools::getValue('cid');
        $sku = Tools::getValue('pid');
        // 获取当前分类对象
        $this->category = new Category(
            $id_category,
            $this->context->language->id
        );
        $category_url = $this->category->getLink() . '?add_first_pro=' . $sku;
        $mobile_detect = new Mobile_Detect();
        if ($mobile_detect->isMobile()) {
            // 设置 User-Agent 为 iPhone
            $options = [
                'http' => [
                    'method' => 'GET',
                    'header' => 'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1'
                ]
            ];
            // 创建上下文选项
            $context = stream_context_create($options);
        } else {
            $context = null;
        }
        $category_info = file_get_contents($category_url, false, $context);
        echo $category_info;
        exit;
    }
}
