<?php

/**
 * Copyright since 2107 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email

 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 *
 *

 * @copyright Since 2107 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

use PrestaShop\PrestaShop\Adapter\Product\PriceFormatter;

class OrderControllerCore extends FrontController
{
    /** @var bool */
    public $ssl = true;
    /** @var string */
    public $php_self = 'order';
    /** @var string */
    public $page_name = 'checkout';
    public $checkoutWarning = [];

    /**
     * @var CheckoutProcess
     */
    protected $checkoutProcess;

    /**
     * @var CartChecksum
     */
    protected $cartChecksum;

    /**
     * Overrides the same parameter in FrontController
     *
     * @var bool automaticallyAllocateInvoiceAddress
     */
    protected $automaticallyAllocateInvoiceAddress = false;

    /**
     * Overrides the same parameter in FrontController
     *
     * @var bool
     */
    protected $automaticallyAllocateDeliveryAddress = false;

    /**
     * Initialize order controller.
     *
     * @see FrontController::init()
     */
    public function init()
    {
        parent::init();
        $this->cartChecksum = new CartChecksum(new AddressChecksum());
    }

    public function postProcess()
    {
        parent::postProcess();
        //重新下单
        if (
            Tools::isSubmit('submitReorder')
            && $this->context->customer->isLogged()
            && $id_order = (int) Tools::getValue('id_order')
        ) {
            //逻辑修改这里reorder是在购物车增加商品
            // 查出所有的购物车信息
            $cart_products = Db::getInstance()->executeS("SELECT * FROM " . _DB_PREFIX_ . "cart_product WHERE id_cart IN (SELECT id_cart FROM " . _DB_PREFIX_ . "orders WHERE id_order = '{$id_order}')");
            if (!$this->context->cart->id) {
                if (Context::getContext()->cookie->id_guest) {
                    $guest = new Guest((int) Context::getContext()->cookie->id_guest);
                    $this->context->cart->mobile_theme = $guest->mobile_theme;
                }
                $this->context->cart->add();
                if (Validate::isLoadedObject($this->context->cart)) {
                    $this->context->cookie->id_cart = (int) $this->context->cart->id;
                }
            }

            $customer_cart_id = $this->context->cart->id;

            $time = date('Y-m-d H:i:s');
            foreach ($cart_products as $cart_product) {
                // 查询产品是否在当前的购物车里
                $record = Db::getInstance()->getRow("SELECT * FROM " . _DB_PREFIX_ . "cart_product WHERE id_cart = '{$customer_cart_id}' AND id_product = '{$cart_product['id_product']}' AND id_product_attribute = '{$cart_product['id_product_attribute']}' AND id_customization = '{$cart_product['id_customization']}'");
                if (!empty($record)) {
                    $tmp_quantity = $record['quantity'] + $cart_product['quantity'];
                    Db::getInstance()->execute("UPDATE " . _DB_PREFIX_ . "cart_product SET quantity = '{$tmp_quantity}' WHERE id_cart_product = '{$record['id_cart_product']}'");
                } else {
                    Db::getInstance()->execute("INSERT INTO " . _DB_PREFIX_ . "cart_product (id_cart, id_product, id_address_delivery, id_shop, id_product_attribute, id_customization, quantity, date_add) VALUES ('{$customer_cart_id}', '{$cart_product['id_product']}', '{$cart_product['id_address_delivery']}', '{$cart_product['id_shop']}', '{$cart_product['id_product_attribute']}', '{$cart_product['id_customization']}', '{$cart_product['quantity']}', '{$time}')");
                    $insert_id_cart_product = Db::getInstance()->Insert_ID();
                    // 查询是不是具有自定义属性
                    $custom_size_record = Db::getInstance()->getRow("SELECT * FROM " . _DB_PREFIX_ . "cart_product_custom_size WHERE id_cart_product = '{$cart_product['id_cart_product']}'");
                    if (!empty($custom_size_record)) {
                        Db::getInstance()->execute(
                            "INSERT INTO " . _DB_PREFIX_ . "cart_product_custom_size
                            (id_cart_product, unit, bust, waist, hips, hollow_to_floor, height, extra_length, arm)
                            VALUES ('{$insert_id_cart_product}',
                            '{$custom_size_record['unit']}',
                            '{$custom_size_record['bust']}',
                            '{$custom_size_record['waist']}',
                            '{$custom_size_record['hips']}',
                            '{$custom_size_record['hollow_to_floor']}',
                            '{$custom_size_record['height']}',
                            '{$custom_size_record['extra_length']}',
                            '{$custom_size_record['arm']}')"
                        );
                    }
                }
            }

            // 重新支付或者购买
            if (Tools::isSubmit('submitPayment')) {
                Tools::redirect($this->context->link->getPageLink('order'));
            } else {
                Tools::redirect('/cart?action=show');
            }
        }
    }

    /**
     * @return CheckoutProcess
     */
    public function getCheckoutProcess()
    {
        return $this->checkoutProcess;
    }

    /**
     * 获取支付session
     * @return CheckoutSession
     */
    public function getCheckoutSession()
    {
        //配送地址查找器
        $deliveryOptionsFinder = new DeliveryOptionsFinder(
            $this->context,
            $this->getTranslator(),
            $this->objectPresenter,
            new PriceFormatter()
        );
        //支付session
        $session = new CheckoutSession(
            $this->context,
            $deliveryOptionsFinder
        );
        return $session;
    }

    /**
     * 重新下单
     *
     * @param CheckoutProcess $process
     */
    protected function restorePersistedData(CheckoutProcess $process)
    {
        $cart = $this->context->cart;
        $customer = $this->context->customer;
        $rawData = Db::getInstance()->getValue(
            'SELECT checkout_session_data FROM ' . _DB_PREFIX_ . 'cart WHERE id_cart = ' . (int) $cart->id
        );
        $data = json_decode($rawData ?? '', true);
        if (!is_array($data)) {
            $data = [];
        }

        $addressValidator = new AddressValidator();
        $invalidAddressIds = $addressValidator->validateCartAddresses($cart);

        // Build the currently selected address' warning message (if relevant)
        if (!$customer->isGuest() && !empty($invalidAddressIds)) {
            $this->checkoutWarning['address'] = [
                'id_address' => (int) reset($invalidAddressIds),
                'exception' => $this->trans(
                    'Your address is incomplete, please update it.',
                    [],
                    'Shop.Notifications.Error'
                ),
            ];
        }

        // Prevent check for guests
        if ($customer->id) {
            // Prepare all other addresses' warning messages (if relevant).
            // These messages are displayed when changing the selected address.
            $allInvalidAddressIds = $addressValidator->validateCustomerAddresses($customer, $this->context->language);
            $this->checkoutWarning['invalid_addresses'] = $allInvalidAddressIds;
        }

        if (isset($data['checksum']) && $data['checksum'] === $this->cartChecksum->generateChecksum($cart)) {
            $process->restorePersistedData($data);
        }
    }

    /**
     * 提交支付的时候确认订单是否仍旧可用
     * @return void
     */
    public function displayAjaxCheckCartStillOrderable(): void
    {
        $responseData = [
            'errors' => false,
            'cartUrl' => '',
        ];
        if (
            $this->context->cart->isAllProductsInStock() !== true ||
            $this->context->cart->checkAllProductsAreStillAvailableInThisState() !== true ||
            $this->context->cart->checkAllProductsHaveMinimalQuantities() !== true
        ) {
            $responseData['errors'] = true;
            $responseData['cartUrl'] = $this->context->link->getPageLink('cart', null, null, ['action' => 'show']);
        }
        header('Content-Type: application/json');
        $this->ajaxRender(json_encode($responseData));
    }

    public function initContent()
    {
        if (Configuration::isCatalogMode()) {
            Tools::redirect('index.php');
        }

        $this->buildCheckoutInfo();

        // 获取购物车信息
        $presentedCart = $this->cart_presenter->present($this->context->cart, true);

        // 验证购物车数据
        $shouldRedirectToCart = false;
        // Check the cart meets minimal order amount treshold
        // Check that the cart is not empty
        if (count($presentedCart['products']) <= 0 || $presentedCart['minimalPurchaseRequired']) {
            $shouldRedirectToCart = true;
        }
        // Check that products are still orderable, at any point in checkout
        if (
            $this->context->cart->checkAllProductsAreStillAvailableInThisState() !== true ||
            $this->context->cart->checkAllProductsHaveMinimalQuantities() !== true
        ) {
            $shouldRedirectToCart = true;
        }
        // If there was a problem, we redirect the user to cart, CartController deals with display of detailed errors
        // We don't redirect in case of ajax requests, so we can get our response
        if ($shouldRedirectToCart === true && !$this->ajax) {
            $cartLink = $this->context->link->getPageLink('cart', null, null, ['action' => 'show']);
            $this->redirectWithNotifications($cartLink);
        }

        parent::initContent();
        if ($this->context->isMobile()) {
            $this->setTemplate('checkout/mobile/checkout');
        } else {
            $this->setTemplate('checkout/pc/checkout');
        }
    }

    public function displayAjaxAddressForm()
    {
        $addressForm = $this->makeAddressForm();

        if (Tools::getIsset('id_address') && ($id_address = (int) Tools::getValue('id_address'))) {
            $addressForm->loadAddressById($id_address);
        }

        if (Tools::getIsset('id_country')) {
            $addressForm->fillWith(['id_country' => Tools::getValue('id_country')]);
        }

        $stepTemplateParameters = [];
        foreach ($this->checkoutProcess->getSteps() as $step) {
            if ($step instanceof CheckoutAddressesStep) {
                $stepTemplateParameters = $step->getTemplateParameters();
            }
        }

        $templateParams = array_merge(
            $addressForm->getTemplateVariables(),
            $stepTemplateParameters,
            ['type' => 'delivery']
        );

        ob_end_clean();
        header('Content-Type: application/json');

        $this->ajaxRender(json_encode([
            'address_form' => $this->render(
                'checkout/_partials/address-form',
                $templateParams
            ),
        ]));
    }

    // 获取国家下的state
    public function displayAjaxStates()
    {
        $id_country = (int) Tools::getValue('id_country');
        $states = State::getStatesByIdCountry($id_country);
        $this->ajaxRender(json_encode([
            'states' => $states,
        ]));
    }

    // Azure Maps
    public function displayAjaxAddressMatch()
    {
        if ($_GET['query'] && $_GET['ct']) {
            $url = 'https://api.radar.io/v1/search/autocomplete';
            $param['query'] = $_GET['query'];
            $param['countryCode'] = $_GET['ct'];
            $header = [
                'Authorization: ' . _PS_RADAR_KEY_,
                'Content-Type: application/json',
            ];
            $res = $this->http($url, $param, 'GET', $header);
            $results = json_decode($res, true);
            if ($results['meta']['code'] == 200) {
                $addresses = [];
                foreach ($results['addresses'] as $item) {
                    $addresses[] = [
                        'address' => $item['addressLabel'] ?? $_GET['query'],
                        'city' => $item['city'] ?? '',
                        'region' => $item['state'] ?? '',
                        'postalCode' => $item['postalCode'] ?? '',
                        'formattedAddress' => $item['formattedAddress'],
                    ];
                }
                echo json_encode($addresses);
            } else {
                echo json_encode([]);
            }
        }
    }

    public function http($url, $params, $method = 'GET', $header = array(), $multi = false)
    {
        $opts = array(
            CURLOPT_TIMEOUT => 30,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => $header
        );
        switch (strtoupper($method)) {
            case 'GET':
                $opts[CURLOPT_URL] = $url . '?' . http_build_query($params);
                break;
            case 'POST':
                $params = $multi ? $params : http_build_query($params);
                $opts[CURLOPT_URL] = $url;
                $opts[CURLOPT_POST] = 1;
                $opts[CURLOPT_POSTFIELDS] = $params;
                break;
            default:
                throw new Exception('error request！');
        }
        $ch = curl_init();
        curl_setopt_array($ch, $opts);
        $data = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        if ($error)
            throw new Exception('error request' . $error);
        return $data;
    }

    // 地址购物车的信息
    protected function buildCheckoutInfo()
    {
        // 获取可用的配送国家
        $availableCountries = Country::getCountries($this->context->language->id, true, false, true, true, true);
        $availableCountries = array_map(function ($country) {
            $country['name'] = Tools::translate($country['name'], 'country');
            return $country;
        }, $availableCountries);
        // 先根据top字段降序，再根据position字段升序，最后根据name字段升序
        usort($availableCountries, function ($a, $b) {
            if ($a['top'] == $b['top']) {
                if ($a['position'] == $b['position']) {
                    return strcmp($a['name'], $b['name']);
                }
                return strcmp($a['position'], $b['position']);
            }
            if ($a['position'] == $b['position']) {
                return strcmp($a['name'], $b['name']);
            }
            return strcmp($a['position'], $b['position']);
        });

        // 支付session
        $checkout_session = $this->getCheckoutSession();

        // 配送地址ID
        $idAddressDelivery = (int) $checkout_session->getIdAddressDelivery();
        // 配送地址
        $delivery_addresses = $this->context->customer->getSimpleAddress($idAddressDelivery, $this->context->language->id);
        if (!$idAddressDelivery || !isset($delivery_addresses['id_country']) || !$delivery_addresses['id_country']) {
            //默认配送地址使用站点对应国家
            $delivery_addresses['id_country'] = $this->context->country->id;
        }
        // 当前地址可用的运输方式
        $delivery_options = $this->context->cart->getDeliveryOptionListForCountry($this->context->language->id, $delivery_addresses['id_country'], $delivery_addresses['city']);
        $weight = $this->context->cart->getTotalWeight();

        // 购物车当前选中的运输方式
        $deliveryOptionKey = $this->context->cart->id_carrier;
        //默认选中运输方式
        
        if($weight == 0){
            foreach ($delivery_options as $delivery_option) {
                $deliveryOptionKey = $delivery_option['id_carrier'];
                break;
            }
        }

        if (!$deliveryOptionKey) {
            $deliveryOptions = $this->context->cart->getDeliveryOption();
            foreach ($deliveryOptions as $deliveryOption) {
                $items = explode(',', $deliveryOption);
                if ($items) {
                    $deliveryOptionKey = $items[0];
                    break;
                }
            }
        }
        //没有选中，默认第一个运输方式
        if (!$deliveryOptionKey) {
            foreach ($delivery_options as $delivery_option) {
                $deliveryOptionKey = $delivery_option['id_carrier'];
                break;
            }
        }

        // 账单地址
        $invoice_addresses = [];
        // 账单地址ID
        $idAddressInvoice = (int) $checkout_session->getIdAddressInvoice();
        if ($idAddressInvoice) {
            $invoice_addresses = $this->context->customer->getSimpleAddress($idAddressInvoice, $this->context->language->id);
        }

        // 是否免单
        $isFree = 0 == (float) $checkout_session->getCart()->getOrderTotal(true, Cart::BOTH);
        // 支付选项查找器
        $paymentOptionsFinder = new PaymentOptionsFinder();
        $conditionsToApproveFinder = new ConditionsToApproveFinder($this->context, $this->getTranslator());
        //支付方式
        $paymentOptions = $paymentOptionsFinder->present($isFree);
        // dd($paymentOptions);
        // var_dump(json_encode($paymentOptions));exit;
        // 多维变一维
        $payment_options = [];
        foreach ($paymentOptions as $options) {
            $payment_options = array_merge($payment_options, $options);
        }
        // 排序, 降序
        usort($payment_options, function ($a, $b) {
            return $b['position'] <=> $a['position'];
        });
        // 添加唯一key
        $id = 0;
        foreach ($payment_options as $k => $option) {
            ++$id;
            $option['id'] = 'payment-option-' . $id;
            $option['key'] = $id;
            $payment_options[$k] = $option;
        }

        //支付条款
        $conditionsToApprove = $conditionsToApproveFinder->getConditionsToApproveForTemplate();

        $this->context->smarty->assign([
            // 地址信息
            'countries' => $availableCountries, // 配送国家
            'countryRegions' => json_encode($availableCountries),   //方便页面使用，不需要每次异步请求数据
            'id_address_delivery' => $idAddressDelivery,
            'id_address_invoice' => $idAddressInvoice,
            'delivery_addresses' => $delivery_addresses,
            'invoice_addresses' => $invoice_addresses,

            // 运输方式
            'delivery_options' => $delivery_options,
            'delivery_option' => $deliveryOptionKey,
            'delivery_message' => $checkout_session->getMessage(),
            'carriers_price' => $this->context->cart->carrierPriceByCountry(),

            // payment
            'is_free' => $isFree,
            'payment_options' => $payment_options, //支付方式
            'conditions_to_approve' => $conditionsToApprove,    //支付条款
            'selected_payment_option' => Tools::getValue('select_payment_option'),
        ]);
    }

    /**
     * Sets controller CSS and JS files.
     *
     * @return bool
     */
    public function setMedia()
    {
        $version = 101;
        parent::setMedia();
        if ($this->context->isMobile()) {
            $this->registerStylesheet('theme-pages-checkout-forms', '/assets/css/pages/checkout-forms.css', ['media' => 'all', 'priority' => 1698, 'version' => $version]);
            $this->registerStylesheet('theme-pages-checkout-reset', '/assets/css/pages/checkout-reset.css', ['media' => 'all', 'priority' => 1799, 'version' => $version]);
            $this->registerStylesheet('theme-pages-checkout-mobile', '/assets/css/pages/checkout-mobile.css', ['media' => 'all', 'priority' => 2100, 'version' => $version]);
        } else {
            $this->registerStylesheet('theme-pages-checkout-pc', '/assets/css/pages/checkout-pc.css', ['media' => 'all', 'priority' => 2100, 'version' => $version]);
        }
        $this->registerJavascript('theme-pages-checkout', '/assets/js/pages/checkout.js', ['position' => 'bottom', 'priority' => 2100, 'version' => $version]);
    }
}
